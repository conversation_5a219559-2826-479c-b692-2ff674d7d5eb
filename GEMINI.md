# GEMINI.md

## 项目概述

这是一个钉钉文档脚本项目，名为“测试人力计划解析器”。其主要功能是自动化处理和分析 Excel 表格中的“测试人力计划”数据。该脚本旨在解析特定结构的 Excel 工作表，提取每个人的每日工作内容、工时、所属行业和项目迭代信息，并生成结构化的分析结果以及两个新的汇总工作表：“个人工时日历(Auto)”和“行业工时日历(Auto)”。

### 核心技术

- **平台**: 钉钉文档脚本环境 (使用 `Workbook`, `Output` 等 API)
- **语言**: JavaScript

## 项目结构

```bash
.
├── assets/                         # 示例数据文件
│   └── 【国内货运】任务计划日历图.xlsx
│   └── 20250811/
│       └── 【国内货运】所有项目测试计划跟进表.xlsx
│       └── 【国内货运】所有项目测试计划跟进表.csv
├── docs/                           # 需求文档
│   ├── 需求文档.md
│   ├── 新需求PRD.md
│   └── 每日人力工作表PRD.md
├── main.js                         # 脚本主入口
└── GEMINI.md                       # 本说明文件
```

## 功能详解

### 1. 数据解析 (`parseTestPlanSheet`)
- **输入**: 一个钉钉文档 `Sheet` 对象。
- **目标**: 解析该工作表中的数据。
- **表头处理**:
    - 支持解析合并的双行表头。
    - 第一行用于识别“项目_迭代”、“事项名称”、“任务描述”、“测试人员”等关键列。
    - 第二行用于识别日期列。
    - 实现了严格和宽松两种表头匹配模式，以适应表头格式的微小变化。
- **数据提取**:
    - 从第1列（索引0）提取行业信息，支持合并单元格。
    - 提取项目迭代、任务名称/描述、测试人员和每日工时。
    - 测试人员支持多种分隔符（逗号、顿号、空格等）。
    - 日期列支持 "M/D" 字符串格式和 Excel 日期序列号格式。
    - 自动过滤空行和无效数据。
- **输出**: 一个包含 `dailyWorkAnalysis` 数组的对象，数组内每个元素代表一天的工作数据。

### 2. 数据合并 (`mergeDailyWorkAnalyses`)
- **功能**: 将多个来源（如“测试任务日历”和“公共事项日历”）解析出的 `dailyWorkAnalysis` 数据合并成一个统一的数据集。

### 3. 统计与输出 (`outputStatistics`)
- **功能**: 打印详细的统计信息到日志。
    - 总体统计：总天数、总任务数、总工时。
    - 人员统计：每个人的总工时。
    - 每日统计：每天的总工时和任务数。
    - 人员每日统计：每个人在每天的工时详情。

### 4. 个人工时日历生成 (`generatePersonalTaskCalendar`)
- **功能**: 根据解析后的 `dailyWorkAnalysis` 数据，创建或更新一个名为“个人工时日历(Auto)”的工作表。
- **结构**:
    - 按人员组织行。
    - 按日期组织列（每个日期包含6个子列：任务描述、单项工时、折算工时、行业、行业合计工时、合计工时）。
- **特性**:
    - 自动合并单元格（如人员姓名、行业、合计工时等）。
    - 根据工时是否超过8小时计算折算工时。
    - 应用预定义的表格样式（颜色、字体、边框、列宽、行高等）。
    - 冻结窗格以便查看。

### 5. 行业工时日历生成 (`generateDailyManpowerSheet`)
- **功能**: 根据解析后的 `dailyWorkAnalysis` 数据，计算按行业维度的每日人力（人数）和工时，并创建或更新一个名为“行业工时日历(Auto)”的工作表。
- **结构**:
    - 按行业组织行。
    - 按日期组织列（每个日期包含2个子列：人力(人数)、工时(小时)）。
- **特性**:
    - 应用预定义的表格样式。
    - 冻结窗格以便查看。

## 配置与常量

脚本的灵活性和可维护性通过一系列常量配置来实现，这些常量定义在 `main.js` 的顶部：

- `SHEET_NAMES`: 定义了源工作表和目标工作表的名称。
- `COLUMN_NAMES`: 定义了关键列的精确名称和行业列的索引。
- `HEADER_KEYWORDS`: 定义了用于表头匹配的关键词（严格和宽松模式）。
- `PERSONNEL_SEPARATORS`: 定义了测试人员姓名的分隔符正则表达式。
- `TABLE_STYLES`: 定义了生成工作表的样式配置（颜色、宽度、高度、格式等）。

## 开发与使用

### 运行方式
- 该脚本设计为在钉钉文档的脚本编辑器中运行。
- 主入口函数是 `main()`，它会自动被调用 (`await main()`)。

### 开发约定
- **模块化**: 核心功能被分解为独立的函数（如 `parseHeaders`, `generateCalendarTable` 等）。
- **配置化**: 关键参数通过文件顶部的常量对象进行配置。
- **健壮性**: 包含广泛的 `try...catch` 错误处理和日志输出 (`Output.log`, `Output.error`)。
- **可读性**: 包含详细的 JSDoc 注释和行内注释。
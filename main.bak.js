/**
 * 钉钉文档脚本 - 测试人力计划解析器
 * 解析"测试人力计划"工作表，提取每个人每天的工作内容和工时
 */

// ==================== 配置常量 ====================

// 工作表名称配置
const SHEET_NAMES = {
  // 输出工作表名称
  PERSONAL_TASK_CALENDAR: "个人工时日历(Auto)",
  DAILY_MANPOWER: "行业工时日历(Auto)"
};

// 源数据配置 - 管理多个源数据和日期所在行
const SOURCE_DATA_CONFIG = [
  {
    sheetName: "测试任务日历",
    dateRow: 1 // 第2行（从0开始）
  },
  {
    sheetName: "公共事项日历",
    dateRow: 0 // 第1行（从0开始）
  }
];

// 列名配置
const COLUMN_NAMES = {
  // 以下为列名精确匹配
  PROJECT_ITERATION: "项目_迭代（与RDMS一致）",
  TASK_NAME: "事项名称",
  TEST_PERSONNEL: "测试人员",
  // 以下为列索引
  INDUSTRY_COLUMN_INDEX: 0 // 行业信息所在列的索引 (通常是第A列)
};

// 表头匹配关键词
const HEADER_KEYWORDS = {
  PROJECT_ITERATION: {
    STRICT: ["项目项目_迭代\n（与RDMS一致）"],
    RELAXED: ["项目_迭代"]
  },
  TASK_NAME: {
    STRICT: ["事项名称"],
    RELAXED: ["事项名称"]
  },
  TASK_DESCRIPTION: {
    STRICT: ["任务描述"],
    RELAXED: ["任务描述"]
  },
  TEST_PERSONNEL: {
    STRICT: ["测试人员"],
    RELAXED: ["测试人员"]
  }
};

// 人员分隔符
const PERSONNEL_SEPARATORS = /[,，、\s.。;；|]+/;

// 表格样式配置
const TABLE_STYLES = {
  HEADER_BACKGROUND_COLOR: '#0171C1',
  SUB_HEADER_BACKGROUND_COLOR: '#F5F5F5',
  ALTERNATE_ROW_BACKGROUND_COLOR: '#FAFAFA',
  FONT_WEIGHT_BOLD: 'bold',
  HEADER_FONT_COLOR: '#FFFFFF',
  LEFT_TITLE_COLUMN_BACKGROUND_COLOR: '#92D04F',
  INDUSTRY_COLUMN_WIDTH: 120,
  DATE_COLUMN_WIDTH: 80,
  NAME_COLUMN_WIDTH: 80,
  TASK_COLUMN_WIDTH: 200,
  HOUR_COLUMN_WIDTH: 120,
  // 用于行业工时日历的"人力(人数)"列宽
  HEADCOUNT_COLUMN_WIDTH: 120,
  ROW_HEIGHT: 25,
  NUMBER_FORMAT_HOURS: '0.00'
};

async function main() {
  console.log("[main] 🚀 开始执行脚本");
  try {
    // 获取所有工作表
    const sheets = Workbook.getSheets();
    console.log("[main] 获取到工作表, sheets:", sheets.map(sheet => sheet.getName()));

    if (sheets.length === 0) {
      Output.error("[main] 错误：工作簿中没有找到任何工作表");
      return;
    }

    // 新的配置驱动逻辑将在下面处理

    // 使用配置数组解析工作表
    const analyses = [];
    console.log("[main] 开始解析工作表, SOURCE_DATA_CONFIG:", SOURCE_DATA_CONFIG);

    SOURCE_DATA_CONFIG.forEach(config => {
      let sheet = null;
      try {
        sheet = Workbook.getSheet(config.sheetName);
        console.log("[main] 获取到工作表, sheet:", sheet.getName());
      } catch (e) {
        // 工作表不存在，跳过
        return;
      }

      if (sheet) {
        // 根据工作表名称确定描述字段偏好
        const descriptionPreference = config.sheetName === "公共事项日历" ? 'taskDescription' : 'taskName';
        const sheetConfig = {
          ...config,
          descriptionPreference,
          sheetLabel: config.sheetName
        };
        const analysis = parseTestPlanSheet(sheet, sheetConfig);
        analyses.push(analysis);
      }
    });

    console.log("[main] 解析工作表完成, analyses:", analyses);

    // 兼容：如果没有找到任何配置的工作表，回退到第一个可用工作表
    if (analyses.length === 0 && sheets.length > 0) {
      const fallback = sheets[0];
      Output.log(`[main] 未找到配置的工作表，使用: ${fallback.getName()}`);
      const fallbackConfig = {
        sheetName: fallback.getName(),
        dateRow: 1, // 默认第2行
        descriptionPreference: 'taskName',
        sheetLabel: fallback.getName()
      };
      const analysis = parseTestPlanSheet(fallback, fallbackConfig);
      analyses.push(analysis);
    }

    // 合并两个工作表的结果
    const workAnalysis = mergeDailyWorkAnalyses(analyses);
    console.log("[main] 合并工作表结果, workAnalysis:", workAnalysis);

    // 输出结果（仅在有数据时）
    if (workAnalysis.dailyWorkAnalysis.length > 0) {
      Output.log("[main] === 解析完成 ===");
    }

    // 输出统计信息
    outputStatistics(workAnalysis);
    console.log("[main] 输出统计信息完成");

    // 输出到个人工时日历工作表
    if (workAnalysis.dailyWorkAnalysis.length > 0) {
      generatePersonalTaskCalendar(workAnalysis.dailyWorkAnalysis);
      console.log("[main] 输出到个人工时日历工作表完成");
    }

    // 输出到行业工时日历工作表
    if (workAnalysis.dailyWorkAnalysis.length > 0) {
      const industryStats = calculateIndustryHeadcountAndHours(workAnalysis.dailyWorkAnalysis);
      if (industryStats.size > 0) {
        generateDailyManpowerSheet(industryStats);
        console.log("[main] 输出到行业工时日历工作表完成");
      }
    }

  } catch (error) {
    Output.error("[main] 脚本执行出错: " + error.message);
    if (error.stack) {
      Output.error("[main] 错误堆栈: " + error.stack);
    }
  }
  console.log("[main] 🚀 脚本执行结束");
}

// ==================== 日期处理辅助函数 ====================

/**
 * 将 Excel 日期序列号转换为 JS Date 对象（遵循 Excel 1900 日期系统）
 * Excel 将 1899-12-30 作为序列号 0（历史上包含 1900-02-29 的兼容性问题），
 * 因此 2 -> 1900-01-01，61 -> 1900-03-01。
 * @param {number} serial - Excel 日期序列号
 * @returns {Date | null} JS Date 对象或 null
 */
function excelSerialToJsDate(serial) {
  if (typeof serial !== 'number' || !Number.isFinite(serial)) return null;
  const dayMs = 24 * 60 * 60 * 1000;
  // 使用 UTC 以避免时区/DST 影响
  const excelEpochUTC = Date.UTC(1899, 11, 30); // 1899-12-30
  const millis = excelEpochUTC + Math.round(serial) * dayMs;
  return new Date(millis);
}

// 已移除: stringToJsDate, jsDateToExcelSerial（统一使用 Excel 原始序列号）

/**
 * 解析测试人力计划工作表
 * @param {Sheet} sheet - 工作表对象
 * @param {Object} config - 配置对象，包含dateRow、descriptionPreference等
 * @returns {Object} 解析后的工作分析数据
 */
function parseTestPlanSheet(sheet, config = {}) {
  const {
    dateRow = 1, // 日期所在行（从0开始）
    descriptionPreference = 'taskName',
    sheetLabel = '未知工作表'
  } = config;
  // 获取一个较大的数据范围 (扩展到208列以支持更多日期列)
  let dataRange;
  let values;

  try {
    // 先尝试获取A1:AZ1000范围的数据
    dataRange = sheet.getRange("A1:AZ1000");
    values = dataRange.getValues();
  } catch (e) {
    // 尝试其他方式
    try {
      // 如果失败，尝试使用数字索引方式获取 (从0开始，1000行，50列)
      dataRange = sheet.getRange(0, 0, 1000, 50);
      values = dataRange.getValues();
    } catch (e2) {
      Output.error("[parseTestPlanSheet] 无法获取数据范围, e2.message: " + e2.message);
      return { dailyWorkAnalysis: [] };
    }
  }

  if (!values || values.length === 0) {
    Output.error("[parseTestPlanSheet] 工作表为空，无法解析数据");
    return { dailyWorkAnalysis: [] };
  }

  // 移除完全空的行（从后往前移除）
  while (values.length > 0 && values[values.length - 1].every(cell => !cell || String(cell).trim() === "")) {
    values.pop();
  }

  // 根据配置的dateRow确定需要的最少行数
  const minRequiredRows = Math.max(dateRow + 1, 2);
  if (!values || values.length < minRequiredRows) {
    Output.error(`[parseTestPlanSheet] 工作表数据行数不足, minRequiredRows: ${minRequiredRows}, actualRows: ${values ? values.length : 0}`);
    return { dailyWorkAnalysis: [] };
  }

  const headerRow1 = values[0];
  const dateHeaderRow = values[dateRow]; // 使用配置的日期行
  const dataStartingRowIndex = Math.max(dateRow + 1, 2); // 数据从日期行之后开始

  // 开始解析工作表

  // 解析表头
  const columnMap = parseHeaders(headerRow1, dateHeaderRow, sheet, sheetLabel);
  console.log("[parseTestPlanSheet] columnMap: ", columnMap);

  // 如果标准匹配不完整，尝试宽松匹配
  if (columnMap.projectIteration === -1 || (columnMap.taskName === -1 && columnMap.taskDescription === -1) || columnMap.testPersonnel === -1) {
    const relaxedColumnMap = parseHeadersRelaxed(headerRow1, dateHeaderRow);

    // 将宽松匹配的结果补充到 columnMap
    if (columnMap.projectIteration === -1) columnMap.projectIteration = relaxedColumnMap.projectIteration;
    if (columnMap.taskName === -1) columnMap.taskName = relaxedColumnMap.taskName;
    if (columnMap.taskDescription === -1) columnMap.taskDescription = relaxedColumnMap.taskDescription;
    if (columnMap.testPersonnel === -1) columnMap.testPersonnel = relaxedColumnMap.testPersonnel;
    if (columnMap.dateColumns.length === 0) columnMap.dateColumns = relaxedColumnMap.dateColumns;
  }

  // 在所有匹配尝试后，再次检查关键列并报告
  if (columnMap.projectIteration === -1 && columnMap.taskName === -1 && columnMap.taskDescription === -1 && columnMap.testPersonnel === -1) {
    Output.error("[parseTestPlanSheet] 最终未能识别任何关键列（项目、任务、人员），请检查表头格式是否正确");
    Output.error(`[parseTestPlanSheet] 表头第1行, headerRow1: ${headerRow1.slice(0, 15).map(h => `"${h}"`).join(", ")}`);
    Output.error(`[parseTestPlanSheet] 日期行, dateHeaderRow: ${dateHeaderRow.slice(0, 15).map(h => `"${h}"`).join(", ")}`);
  }

  // 检查日期列
  if (columnMap.dateColumns.length === 0) {
    Output.error(`[parseTestPlanSheet] 在${sheetLabel}的日期行中未找到日期列，请检查日期格式是否为Excel日期序列号（40000-80000）`);
    Output.error(`[parseTestPlanSheet] 日期行内容, dateHeaderRow: ${dateHeaderRow.slice(0, 15).map(h => `"${h}"`).join(", ")}`);
    const sampleNumbers = dateHeaderRow.filter(h => typeof h === 'number' && h >= 40000 && h <= 80000).slice(0, 3);
    if (sampleNumbers.length > 0) {
      Output.log(`[parseTestPlanSheet] 发现可能的日期序号, sampleNumbers: ${sampleNumbers.join(", ")}`);
    }
  } else {
    Output.log(`[parseTestPlanSheet] 在${sheetLabel}中找到${columnMap.dateColumns.length}个日期列`);
  }

  // 表头解析完成

  // 解析数据行
  const dailyWorkMap = new Map(); // Map<number(serial), { dateSerial: number, tasks: Task[] }>
  let processedRows = 0;
  let validRows = 0;
  let lastProjectIteration = ""; // 记录上一行的项目迭代信息，用于处理合并单元格
  let lastIndustry = ""; // 记录上一行的行业信息，用于处理合并单元格

  if (values.length <= dataStartingRowIndex) {
    // 无数据行
  }

  for (let i = dataStartingRowIndex; i < values.length; i++) {
    const row = values[i];
    processedRows++;

    // 跳过空行
    if (isEmptyRow(row)) {
      continue;
    }

    // 提取任务基本信息，传入上一行的项目迭代信息用于处理合并单元格
    const taskInfo = extractTaskInfo(row, columnMap, lastProjectIteration, descriptionPreference);

    // 提取行业信息 - 对于公共事项日历，使用事项内容作为行业
    let currentIndustry = "";
    let industry = "";
    
    if (config.sheetName === "公共事项日历") {
      // 【修改】公共事项日历没有行业字段，使用公共事项内容填充行业
      // 从任务描述中提取行业信息
      const taskContent = taskInfo.taskDescription || "";
      if (taskContent) {
        // 提取任务名称部分作为行业（去掉项目迭代前缀）
        const parts = taskContent.split(' - ');
        if (parts.length > 1) {
          currentIndustry = parts[1].trim(); // 使用事项名称作为行业
        } else {
          currentIndustry = taskContent.trim(); // 如果没有分隔符，使用整个内容
        }
      }
      industry = currentIndustry || "公共事项";
    } else {
      // 对于其他表格，使用原有逻辑从第一列提取行业信息
      currentIndustry = String(row[COLUMN_NAMES.INDUSTRY_COLUMN_INDEX] || "").trim();
      industry = currentIndustry || lastIndustry;
      if (currentIndustry) {
        lastIndustry = currentIndustry;
      }
    }

    // 更新上一行的项目迭代信息
    if (taskInfo.currentProjectIteration) {
      lastProjectIteration = taskInfo.currentProjectIteration;
    }

    // 跳过调试输出

    if (!taskInfo.taskDescription) {
      continue; // 跳过没有任务描述的行
    }

    validRows++;

    // 处理每个日期列的工时数据
    columnMap.dateColumns.forEach(dateCol => {
      const workHours = parseFloat(row[dateCol.index]) || 0;

      if (workHours > 0) {
        const serial = dateCol.serial;
        const dateKey = serial;

        // 初始化日期数据
        if (!dailyWorkMap.has(dateKey)) {
          dailyWorkMap.set(dateKey, { dateSerial: serial, tasks: [] });
        }

        // 【修改】处理任务描述 - 对于公共事项日历，避免重复显示
        let finalTaskDescription = taskInfo.taskDescription;
        if (config.sheetName === "公共事项日历" && taskInfo.taskDescription) {
          // 对于公共事项日历，如果行业已经是事项名称，则简化任务描述显示
          const parts = taskInfo.taskDescription.split(' - ');
          if (parts.length > 1 && parts[1].trim() === industry) {
            // 如果事项名称已经作为行业，则任务描述只显示项目迭代部分
            finalTaskDescription = parts[0].trim() || taskInfo.taskDescription;
          }
        }

        // 添加任务数据
        dailyWorkMap.get(dateKey).tasks.push({
          industry: industry,
          taskDescription: finalTaskDescription,
          testPersonnel: taskInfo.testPersonnel,
          workHours: workHours
        });
      }
    });
  }

  // 数据处理结果检查
  if (validRows === 0 && processedRows > 0) {
    Output.error("[parseTestPlanSheet] 处理了数据行，但未找到包含有效任务描述或工时的数据。请检查数据内容和格式");
  } else if (validRows > 0 && dailyWorkMap.size === 0) {
    Output.error("[parseTestPlanSheet] 找到了有效的任务描述，但未找到任何有效的工时数据。请检查日期列中的工时数值");
  } else if (validRows > 0 || dailyWorkMap.size > 0) {
    Output.log(`[parseTestPlanSheet] 解析完成, validRows: ${validRows}, workDays: ${dailyWorkMap.size}`);
  }

  // 转换为最终格式
  const dailyWorkAnalysis = Array.from(dailyWorkMap.values())
    .sort((a, b) => a.dateSerial - b.dateSerial);

  return { dailyWorkAnalysis };
}

/**
 * 将列索引转换为Excel列名 (0->A, 1->B, 25->Z, 26->AA, 27->AB...)
 * @param {number} index - 列索引 (从0开始)
 * @returns {string} Excel列名
 */
function indexToColumnName(index) {
  let columnName = '';
  while (index >= 0) {
    columnName = String.fromCharCode((index % 26) + 65) + columnName;
    index = Math.floor(index / 26) - 1;
  }
  return columnName;
}

/**
 * 解析合并的表头，识别各列位置
 * @param {Array} headerRow1 - 表头第一行
 * @param {Array} dateHeaderRow - 日期所在行
 * @param {Sheet} sheet - 工作表对象 (用于获取sheet名称)
 * @param {string} sheetLabel - 工作表标签（用于日志）
 * @returns {Object} 列映射对象
 */
function parseHeaders(headerRow1, dateHeaderRow, sheet, sheetLabel = '未知') {
  const columnMap = {
    projectIteration: -1,  // 项目_迭代（与RDMS一致）
    taskName: -1,          // 事项名称
    taskDescription: -1,   // 任务描述（用于“公共事项日历”）
    testPersonnel: -1,     // 测试人员
    dateColumns: []        // 日期列（使用 Excel 原始序列号）
  };

  const numCols = Math.max(headerRow1 ? headerRow1.length : 0, dateHeaderRow ? dateHeaderRow.length : 0);

  for (let index = 0; index < numCols; index++) {
    const header1Str = String(headerRow1[index] || "").trim();
    const dateHeaderValue = dateHeaderRow[index];

    // 在第一行查找常规列
    if (HEADER_KEYWORDS.PROJECT_ITERATION.STRICT.every(kw => header1Str.includes(kw))) {
      columnMap.projectIteration = index;
    } else if (HEADER_KEYWORDS.TASK_NAME.STRICT.every(kw => header1Str.includes(kw))) {
      columnMap.taskName = index;
    } else if (HEADER_KEYWORDS.TASK_DESCRIPTION && HEADER_KEYWORDS.TASK_DESCRIPTION.STRICT.every(kw => header1Str.includes(kw))) {
      columnMap.taskDescription = index;
    } else if (HEADER_KEYWORDS.TEST_PERSONNEL.STRICT.every(kw => header1Str.includes(kw))) {
      columnMap.testPersonnel = index;
    }

    // 在日期行查找日期列：使用新的日期数值范围检测（40000-80000）
    if (typeof dateHeaderValue === 'number' && Number.isFinite(dateHeaderValue)) {
      // 更新日期范围检测：40000 < date < 80000
      if (dateHeaderValue > 40000 && dateHeaderValue < 80000) {
        columnMap.dateColumns.push({ index: index, serial: Math.round(dateHeaderValue) });
        // 调试日志：显示检测到的日期序列号和对应的日期
        const convertedDate = excelSerialToJsDate(dateHeaderValue);
        if (convertedDate) {
          const month = convertedDate.getUTCMonth() + 1;
          const day = convertedDate.getUTCDate();
          const columnName = indexToColumnName(index);
          Output.log(`[parseHeaders] 检测到日期列, index: ${index}, sheetLabel: ${sheetLabel}, columnName: ${columnName}, serial: ${dateHeaderValue}, date: ${month}/${day}`);
        }
      } else {
        // 调试日志：显示被忽略的数字
        const columnName = indexToColumnName(index);
        Output.log(`[parseHeaders] 忽略数字列, index: ${index}, sheetLabel: ${sheetLabel}, columnName: ${columnName}, value: ${dateHeaderValue} (超出日期范围40000-80000)`);
      }
    }
  }

  return columnMap;
}

/**
 * 宽松匹配合并的表头
 * @param {Array} headerRow1 - 表头第一行
 * @param {Array} dateHeaderRow - 日期所在行
 * @returns {Object} 列映射对象
 */
function parseHeadersRelaxed(headerRow1, dateHeaderRow) {
  const columnMap = {
    projectIteration: -1,
    taskName: -1,
    taskDescription: -1,
    testPersonnel: -1,
    dateColumns: []
  };

  const numCols = Math.max(headerRow1 ? headerRow1.length : 0, dateHeaderRow ? dateHeaderRow.length : 0);

  for (let index = 0; index < numCols; index++) {
    const header1Str = String(headerRow1[index] || "").trim();
    const header1StrLower = header1Str.toLowerCase();
    const dateHeaderValue = dateHeaderRow[index];

    // 宽松匹配常规列（在第一行）
    if (HEADER_KEYWORDS.PROJECT_ITERATION.RELAXED.some(kw => header1StrLower.includes(kw))) {
      if (columnMap.projectIteration === -1) columnMap.projectIteration = index;
    }

    if (HEADER_KEYWORDS.TASK_NAME.RELAXED.some(kw => header1StrLower.includes(kw))) {
      if (columnMap.taskName === -1) columnMap.taskName = index;
    }

    if (HEADER_KEYWORDS.TASK_DESCRIPTION && HEADER_KEYWORDS.TASK_DESCRIPTION.RELAXED.some(kw => header1StrLower.includes(kw))) {
      if (columnMap.taskDescription === -1) columnMap.taskDescription = index;
    }

    if (HEADER_KEYWORDS.TEST_PERSONNEL.RELAXED.some(kw => header1StrLower.includes(kw))) {
      if (columnMap.testPersonnel === -1) columnMap.testPersonnel = index;
    }

    // 在日期行查找日期列：使用新的日期数值范围检测（40000-80000）
    if (typeof dateHeaderValue === 'number' && Number.isFinite(dateHeaderValue)) {
      // 更新日期范围检测：40000 < date < 80000
      if (dateHeaderValue > 40000 && dateHeaderValue < 80000) {
        columnMap.dateColumns.push({ index: index, serial: Math.round(dateHeaderValue) });
        // 调试日志：显示检测到的日期序列号和对应的日期
        const convertedDate = excelSerialToJsDate(dateHeaderValue);
        if (convertedDate) {
          const month = convertedDate.getUTCMonth() + 1;
          const day = convertedDate.getUTCDate();
          Output.log(`[parseHeadersRelaxed] 宽松匹配检测到日期列, index: ${index}, serial: ${dateHeaderValue}, date: ${month}/${day}`);
        }
      }
    }
  }

  return columnMap;
}

/**
 * 提取任务基本信息
 * @param {Array} row - 数据行
 * @param {Object} columnMap - 列映射
 * @param {string} lastProjectIteration - 上一行的项目迭代信息（用于处理合并单元格）
 * @returns {Object} 任务信息
 */
function extractTaskInfo(row, columnMap, lastProjectIteration = "", descriptionPreference = 'taskName') {
  // 提取当前行的项目迭代信息
  const currentProjectIteration = columnMap.projectIteration >= 0 ?
    String(row[columnMap.projectIteration] || "").trim() : "";

  // 决定使用哪个项目迭代信息：优先使用当前行，如果为空则使用上一行的
  const projectIteration = currentProjectIteration || lastProjectIteration;

  // 提取基础描述字段：根据不同sheet取“事项名称”或“任务描述”，必要时兜底
  const taskName = columnMap.taskName >= 0 ? String(row[columnMap.taskName] || "").trim() : "";
  const taskDescField = columnMap.taskDescription >= 0 ? String(row[columnMap.taskDescription] || "").trim() : "";
  const baseDescription = (descriptionPreference === 'taskDescription')
    ? (taskDescField || taskName)
    : (taskName || taskDescField);

  // 拼接任务描述
  let taskDescription = "";
  if (projectIteration && baseDescription) {
    taskDescription = `${projectIteration} - ${baseDescription}`;
  } else if (projectIteration) {
    taskDescription = projectIteration;
  } else if (baseDescription) {
    taskDescription = baseDescription;
  }

  // 提取测试人员
  const testPersonnelStr = columnMap.testPersonnel >= 0 ?
    String(row[columnMap.testPersonnel] || "").trim() : "";

  const testPersonnel = testPersonnelStr ?
    testPersonnelStr.split(PERSONNEL_SEPARATORS).filter(name => name.length > 0) : [];

  return {
    taskDescription,
    testPersonnel,
    currentProjectIteration // 返回当前行的项目迭代信息，用于更新lastProjectIteration
  };
}

/**
 * 合并多个每日工作分析结果
 * @param {Array<{dailyWorkAnalysis: Array}>} analyses - 分析结果数组
 * @returns {{ dailyWorkAnalysis: Array }} 合并后的结果
 */
function mergeDailyWorkAnalyses(analyses) {
  const mergedMap = new Map(); // Map<number(serial), { dateSerial: number, tasks: Task[] }>

  analyses.filter(Boolean).forEach(analysis => {
    const list = (analysis && analysis.dailyWorkAnalysis) || [];
    list.forEach(day => {
      const dateKey = day.dateSerial;
      if (!mergedMap.has(dateKey)) {
        mergedMap.set(dateKey, { dateSerial: dateKey, tasks: [] });
      }
      const entry = mergedMap.get(dateKey);
      // 合并任务列表
      if (Array.isArray(day.tasks) && day.tasks.length > 0) {
        entry.tasks.push(...day.tasks);
      }
    });
  });

  const dailyWorkAnalysis = Array.from(mergedMap.values())
    .sort((a, b) => a.dateSerial - b.dateSerial);

  return { dailyWorkAnalysis };
}

/**
 * 判断是否为空行
 * @param {Array} row - 数据行
 * @returns {boolean} 是否为空行
 */
function isEmptyRow(row) {
  return row.every(cell => {
    const cellValue = String(cell || "").trim();
    return cellValue === "" || cellValue === "0";
  });
}

/**
 * 比较日期字符串（简单的月/日格式）
 * @param {string} date1 - 日期1
 * @param {string} date2 - 日期2
 * @returns {number} 比较结果
 */
// 已移除: compareDates（按序列号排序）

/**
 * 输出统计信息
 * @param {Object} workAnalysis - 工作分析数据
 */
function outputStatistics(workAnalysis) {
  Output.log("[outputStatistics] === 统计信息 ===");

  const { dailyWorkAnalysis } = workAnalysis;

  if (dailyWorkAnalysis.length === 0) {
    Output.error("[outputStatistics] 无统计数据");
    return;
  }

  // 总体统计
  const totalDays = dailyWorkAnalysis.length;
  const totalTasks = dailyWorkAnalysis.reduce((sum, day) => sum + day.tasks.length, 0);
  const totalWorkHours = dailyWorkAnalysis.reduce((sum, day) =>
    sum + day.tasks.reduce((daySum, task) => daySum + task.workHours, 0), 0);

  Output.log(`[outputStatistics] 总天数: ${totalDays}`);
  Output.log(`[outputStatistics] 总任务数: ${totalTasks}`);
  Output.log(`[outputStatistics] 总工时: ${totalWorkHours.toFixed(1)} 小时`);

  // 人员工时统计
  const personnelStats = new Map();
  dailyWorkAnalysis.forEach(day => {
    day.tasks.forEach(task => {
      task.testPersonnel.forEach(person => {
        if (!personnelStats.has(person)) {
          personnelStats.set(person, 0);
        }
        personnelStats.set(person, personnelStats.get(person) + task.workHours);
      });
    });
  });

  if (personnelStats.size > 0) {
    Output.log("[outputStatistics] 人员工时统计:");
    Array.from(personnelStats.entries())
      .sort((a, b) => b[1] - a[1])
      .forEach(([person, hours]) => {
        Output.log(`[outputStatistics] 人员工时, person: ${person}, hours: ${hours.toFixed(1)}`);
      });
  }

  // 每日工时统计
  Output.log("[outputStatistics] 每日工时统计:");
  dailyWorkAnalysis.forEach(day => {
    const dayTotal = day.tasks.reduce((sum, task) => sum + task.workHours, 0);
    Output.log(`[outputStatistics] 每日工时, date: ${formatDateSerialForDisplay(day.dateSerial)}, hours: ${dayTotal.toFixed(1)}, tasks: ${day.tasks.length}`);
  });

  // 人员每日工时统计
  if (personnelStats.size > 0) {
    Output.log("[outputStatistics] 人员每日工时统计:");

    // 构建人员每日工时矩阵
    const personnelDailyStats = new Map(); // Map<Person, Map<number, number>>
    dailyWorkAnalysis.forEach(day => {
      const dateKey = day.dateSerial;
      day.tasks.forEach(task => {
        task.testPersonnel.forEach(person => {
          if (!personnelDailyStats.has(person)) {
            personnelDailyStats.set(person, new Map());
          }
          const personDailyMap = personnelDailyStats.get(person);
          const currentHours = personDailyMap.get(dateKey) || 0;
          personDailyMap.set(dateKey, currentHours + task.workHours);
        });
      });
    });

    // 获取所有日期并排序
    const allDates = dailyWorkAnalysis.map(day => day.dateSerial).sort((a, b) => a - b);

    // 按人员输出每日工时
    Array.from(personnelDailyStats.entries())
      .sort((a, b) => b[1].size - a[1].size || a[0].localeCompare(b[0])) // 按工作天数降序，然后按姓名排序
      .forEach(([person, dailyMap]) => {
        const dailyDetails = allDates.map(serial => {
          const hours = dailyMap.get(serial) || 0;
          return hours > 0 ? `${formatDateSerialForDisplay(serial)}:${hours.toFixed(1)}` : null;
        }).filter(detail => detail !== null);

        const totalPersonHours = Array.from(dailyMap.values()).reduce((sum, hours) => sum + hours, 0);
        Output.log(`[outputStatistics] 人员每日工时, person: ${person}, totalHours: ${totalPersonHours.toFixed(1)}, details: ${dailyDetails.join(", ")}`);
      });
  }
}

/**
 * 格式化日期数字用于日志输出
 * @param {Date} jsDate - JS Date对象
 * @returns {string} 格式化后的日期字符串 "M/D"
 */
function formatDateForDisplay(jsDate) {
  if (!(jsDate instanceof Date)) return "";
  const month = jsDate.getUTCMonth() + 1;
  const day = jsDate.getUTCDate();
  return `${month}/${day}`;
}

/**
 * 将 Excel 序列号转换为用于日志输出的 M/D 字符串
 * 仅用于展示，不做任何业务计算
 */
function formatDateSerialForDisplay(serial) {
  const date = excelSerialToJsDate(serial);
  return formatDateForDisplay(date);
}

/**
 * 生成个人工时日历工作表
 * @param {Array} dailyWorkAnalysis - 每日工作分析数据
 */
function generatePersonalTaskCalendar(dailyWorkAnalysis) {
  try {
    // 检查并删除已存在的工作表
    const existingSheet = Workbook.getSheet(SHEET_NAMES.PERSONAL_TASK_CALENDAR);
    if (existingSheet) {
      try {
        Workbook.deleteSheet(existingSheet);
      } catch (e) {
        // 忽略删除错误
      }
    }

    // 创建新工作表
    const calendarSheet = Workbook.insertSheet(SHEET_NAMES.PERSONAL_TASK_CALENDAR);
    if (!calendarSheet) {
      Output.error(`[generatePersonalTaskCalendar] 无法创建工作表，跳过生成, sheetName: ${SHEET_NAMES.PERSONAL_TASK_CALENDAR}`);
      return;
    }

    // 获取所有日期并排序
    const allDates = Array.from(new Set(dailyWorkAnalysis.map(day => day.dateSerial)))
      .sort((a, b) => a - b);

    // 构建人员任务数据结构
    const personnelTaskData = buildPersonnelTaskData(dailyWorkAnalysis, allDates);

    // 生成表格数据
    generateCalendarTable(calendarSheet, personnelTaskData, allDates);

    // 冻结标题行与标题列
    try {
      applyFreezePanes(calendarSheet, 2, 1);
    } catch (_freezeErr) { void _freezeErr; }

    Output.log("[generatePersonalTaskCalendar] 个人工时日历生成完成");

  } catch (error) {
    Output.error("[generatePersonalTaskCalendar] 生成个人工时日历工作表时出错, error: " + error.message);
  }
}

/**
 * 构建人员任务数据结构
 * @param {Array} dailyWorkAnalysis - 每日工作分析数据
 * @param {Array} allDates - 所有日期
 * @returns {Object} 人员任务数据
 */
function buildPersonnelTaskData(dailyWorkAnalysis, _allDates) {
  const personnelTaskData = new Map(); // Map<Person, Map<number(serial), Task[]>>

  // 遍历每天的数据
  dailyWorkAnalysis.forEach(day => {
    const dateKey = day.dateSerial;
    day.tasks.forEach(task => {
      task.testPersonnel.forEach(person => {
        if (!personnelTaskData.has(person)) {
          personnelTaskData.set(person, new Map());
        }
        const personMap = personnelTaskData.get(person);
        if (!personMap.has(dateKey)) {
          personMap.set(dateKey, []);
        }

        personMap.get(dateKey).push({
          taskDescription: task.taskDescription,
          workHours: task.workHours,
          industry: task.industry
        });
      });
    });
  });

  return personnelTaskData;
}

/**
 * 生成日历表格
 * @param {Sheet} sheet - 工作表对象
 * @param {Object} personnelTaskData - 人员任务数据
 * @param {Array} allDates - 所有日期
 */
function generateCalendarTable(sheet, personnelTaskData, allDates) {
  // 清空操作已移至上层函数，此处不再执行

  let currentRow = 1;

  // 生成日期标题行
  currentRow = generateDateHeaders(sheet, allDates, currentRow);

  // 生成列标题行
  currentRow = generateColumnHeaders(sheet, allDates, currentRow);

  // 生成人员数据
  const sortedPersonnel = Array.from(personnelTaskData.keys()).sort();
  sortedPersonnel.forEach(person => {
    currentRow = generatePersonData(sheet, person, personnelTaskData.get(person), allDates, currentRow);
  });

  // 应用表格样式
  applyTableStyles(sheet, allDates, currentRow - 1);

  // 表格生成完成
}

/**
 * 生成日期标题行
 * @param {Sheet} sheet - 工作表对象
 * @param {Array} allDates - 所有日期
 * @param {number} startRow - 起始行
 * @returns {number} 下一行的位置
 */
function generateDateHeaders(sheet, allDates, startRow) {
  // 第一行：日期标题（直接写入 Excel 序列号）
  allDates.forEach((serial, index) => {
    const colOffset = index * 6 + 1; // 每个日期占6列，从第2列开始（留出姓名列）

    try {
      const dateCell = sheet.getCell(startRow - 1, colOffset);
      dateCell.setValue(serial);
      dateCell.setNumberFormat('m"月"d"日"');

      // 合并日期标题单元格（跨6列）
      const dateHeaderRange = sheet.getRange(startRow - 1, colOffset, 1, 6);
      dateHeaderRange.merge();

    } catch (e) {
      Output.error(`[generateDateHeaders] 设置日期标题时出错, date: ${formatDateSerialForDisplay(serial)}, error: ${e.message}`);
    }
  });

  return startRow + 1;
}

/**
 * 生成列标题行
 * @param {Sheet} sheet - 工作表对象
 * @param {Array} allDates - 所有日期
 * @param {number} startRow - 起始行
 * @returns {number} 下一行的位置
 */
function generateColumnHeaders(sheet, allDates, startRow) {
  try {
    // 设置姓名列标题
    sheet.getCell(startRow - 1, 0).setValue("姓名");

    // 为每个日期设置列标题
    allDates.forEach((date, index) => {
      const colOffset = index * 6 + 1; // 每个日期占6列

      sheet.getCell(startRow - 1, colOffset).setValue("任务描述");
      sheet.getCell(startRow - 1, colOffset + 1).setValue("单项工时(小时)");
      sheet.getCell(startRow - 1, colOffset + 2).setValue("折算工时(小时)");
      sheet.getCell(startRow - 1, colOffset + 3).setValue("行业");
      sheet.getCell(startRow - 1, colOffset + 4).setValue("行业合计工时(小时)");
      sheet.getCell(startRow - 1, colOffset + 5).setValue("合计工时(小时)");
    });
  } catch (e) {
    Output.error(`[generateColumnHeaders] 设置列标题时出错, error: ${e.message}`);
  }

  return startRow + 1;
}

/**
 * 生成人员数据
 * @param {Sheet} sheet - 工作表对象
 * @param {string} person - 人员姓名
 * @param {Object} personData - 人员数据
 * @param {Array} allDates - 所有日期
 * @param {number} startRow - 起始行
 * @returns {number} 下一行的位置
 */
function generatePersonData(sheet, person, personDataMap, allDates, startRow) {
  try {
    let maxRowsForPerson = 1;

    // 计算这个人需要的最大行数（基于任务最多的那一天）
    allDates.forEach(serial => {
      const tasks = personDataMap.get(serial) || [];
      maxRowsForPerson = Math.max(maxRowsForPerson, tasks.length);
    });

    // 设置人员姓名（只在第一行）
    sheet.getCell(startRow - 1, 0).setValue(person);

    // 如果这个人占用多行，合并姓名单元格
    if (maxRowsForPerson > 1) {
      const nameRange = sheet.getRange(startRow - 1, 0, maxRowsForPerson, 1);
      nameRange.merge();
    }

    // 为每个日期生成任务数据（扩展为6列：任务描述、单项工时、折算工时、行业、行业合计工时、合计工时）
    allDates.forEach((serial, dateIndex) => {
      const originalTasks = personDataMap.get(serial) || [];
      const colOffset = dateIndex * 6 + 1;

      // 计算当天总工时（实际值，用于“合计工时(小时)”）
      // 先按“行业 -> 任务描述”排序，确保同一行业相邻，且行业内按任务描述有序，便于后续合并
      const normalize = (text) => String(text || '').trim().replace(/\s+/g, ' ').toLowerCase();
      const tasks = [...originalTasks].sort((a, b) => {
        const ai = normalize(a.industry);
        const bi = normalize(b.industry);
        // 将空行业放到最后
        const aEmpty = ai === '';
        const bEmpty = bi === '';
        if (aEmpty !== bEmpty) return aEmpty ? 1 : -1;
        if (ai < bi) return -1;
        if (ai > bi) return 1;
        const ad = normalize(a.taskDescription);
        const bd = normalize(b.taskDescription);
        if (ad < bd) return -1;
        if (ad > bd) return 1;
        return 0;
      });

      const dayTotal = tasks.reduce((sum, task) => sum + (parseFloat(task.workHours) || 0), 0);
      const scaleFactor = dayTotal > 8 ? (8 / dayTotal) : 1; // 超过8小时则按比例折算

      if (tasks.length === 0) {
        // 没有任务的日期，显示空值
        sheet.getCell(startRow - 1, colOffset).setValue("");
        sheet.getCell(startRow - 1, colOffset + 1).setValue("");
        sheet.getCell(startRow - 1, colOffset + 2).setValue("");
        sheet.getCell(startRow - 1, colOffset + 3).setValue("");
        sheet.getCell(startRow - 1, colOffset + 4).setValue("");
        sheet.getCell(startRow - 1, colOffset + 5).setValue("");
      } else {
        // 计算每个行业在当天（该人员）的实际工时总和
        const industryTotalMap = new Map(); // Map<string(normalized), number>
        tasks.forEach(t => {
          const key = normalize(t.industry);
          const prev = industryTotalMap.get(key) || 0;
          industryTotalMap.set(key, prev + (parseFloat(t.workHours) || 0));
        });

        // 识别“行业”的连续区间（仅合并连续相同行业的任务）
        const runs = []; // { industry(normalized), startIndex, count }
        let runStart = 0;
        while (runStart < tasks.length) {
          const industry = normalize(tasks[runStart].industry);
          let runEnd = runStart + 1;
          while (runEnd < tasks.length) {
            const curIndustry = normalize(tasks[runEnd].industry);
            if (curIndustry !== industry) break;
            runEnd++;
          }
          runs.push({ industry, startIndex: runStart, count: runEnd - runStart });
          runStart = runEnd;
        }

        // 写入任务行
        tasks.forEach((task, taskIndex) => {
          const rowIndex = startRow + taskIndex - 1;

          const singleHours = parseFloat(task.workHours) || 0;
          const convertedHours = singleHours * scaleFactor;

          sheet.getCell(rowIndex, colOffset).setValue(task.taskDescription);
          sheet.getCell(rowIndex, colOffset + 1).setValue(singleHours);
          sheet.getCell(rowIndex, colOffset + 2).setValue(convertedHours);
          sheet.getCell(rowIndex, colOffset + 3).setValue(String(task.industry || ""));

          // 如果该行是某个行业连续区间的起始行，则做合并并写入行业合计工时（实际值）
          const run = runs.find(r => r.startIndex === taskIndex);
          if (run) {
            if (run.count > 1) {
              try {
                const industryRange = sheet.getRange(rowIndex, colOffset + 3, run.count, 1);
                industryRange.merge();
              } catch (_e) { void _e; }
            }

            const industryTotalActual = industryTotalMap.get(run.industry) || 0;
            const industryTotalCell = sheet.getCell(rowIndex, colOffset + 4);
            industryTotalCell.setValue(industryTotalActual);

            // 如果行业合计工时超过8小时，设置文本加粗红色
            if (industryTotalActual > 8) {
              industryTotalCell.setFontWeight('bold');
              industryTotalCell.setFontColor('#FF0000');
            }

            if (run.count > 1) {
              try {
                const industryTotalRange = sheet.getRange(rowIndex, colOffset + 4, run.count, 1);
                industryTotalRange.merge();
              } catch (_e2) { void _e2; }
            }
          }

          // 合计工时（当天所有任务的实际工时总和，仅写在第一行，并做合并）
          if (taskIndex === 0) {
            const totalCell = sheet.getCell(rowIndex, colOffset + 5);
            totalCell.setValue(dayTotal);

            // 如果当天合计工时超过8小时，设置文本加粗红色
            if (dayTotal > 8) {
              totalCell.setFontWeight('bold');
              totalCell.setFontColor('#FF0000');
            }

            // 合并个人剩余空格（将该人当天的所有行在合计工时列合并）
            if (maxRowsForPerson > 1) {
              const totalRange = sheet.getRange(rowIndex, colOffset + 5, maxRowsForPerson, 1);
              try { totalRange.merge(); } catch (_e3) { void _e3; }
            }
          }
        });
      }
    });

    // 合并每个人员在每个日期的所有列（任务项和空行占位符）
    // 对于每个人员的每个日期，将该日期对应的所有列从第一行合并到最大行
    for (let dateIndex = 0; dateIndex < allDates.length; dateIndex++) {
      const colOffset = dateIndex * 6 + 1;  // 每个日期从第2列开始，每6列一个日期
      const personStartRow = startRow - 1;  // 人员数据开始行

      // 为该日期的6列进行合并（任务描述、单项工时、折算工时、行业、行业合计工时、合计工时）
      for (let col = 0; col < 6; col++) {
        const currentCol = colOffset + col;

        try {
          // 合并从该人员第一行开始到最大行的单元格
          // 范围是从 personStartRow 开始，跨越 maxRowsForPerson 行，1列宽
          if (maxRowsForPerson > 1) {
            const mergeRange = sheet.getRange(personStartRow, currentCol, maxRowsForPerson, 1);
            mergeRange.merge();
          }
        } catch (_e) {
          // 合并可能失败，例如单元格已经被合并，忽略错误
          void _e;
        }
      }
    }

    return startRow + maxRowsForPerson;

  } catch (e) {
    Output.error(`[generatePersonData] 生成人员数据时出错, person: ${person}, error: ${e.message}`);
    return startRow + 1;
  }
}

/**
 * 应用表格样式
 * @param {Sheet} sheet - 工作表对象
 * @param {Array} allDates - 所有日期
 * @param {number} totalRows - 总行数
 */
function applyTableStyles(sheet, allDates, totalRows) {
  try {
    const totalCols = allDates.length * 6 + 1; // 日期列数 * 6 + 姓名列

    // 获取整个表格范围
    const _tableRange = sheet.getRange(0, 0, totalRows, totalCols);

    // 设置表格边框和交替行色
    try {
      // 为数据区域设置交替行色（从第3行开始）
      for (let row = 2; row < totalRows; row++) {
        if (row % 2 === 0) { // 偶数行
          try {
            const rowRange = sheet.getRange(row, 0, 1, totalCols);
            rowRange.setBackgroundColor(TABLE_STYLES.ALTERNATE_ROW_BACKGROUND_COLOR);
          } catch (_bgError) {
            void _bgError; // ignore
          }
        }
      }

      // 交替行色设置完成
    } catch (_e) {
      // 忽略样式设置错误
    }

    // 设置标题行样式（第1行和第2行）
    if (totalRows >= 2) {
      try {
        // 第一行（日期标题行）样式
        const dateHeaderRange = sheet.getRange(0, 0, 1, totalCols);
        dateHeaderRange.setFontWeight(TABLE_STYLES.FONT_WEIGHT_BOLD);

        // 尝试设置背景色（顶端标题行）
        try {
          dateHeaderRange.setBackgroundColor(TABLE_STYLES.HEADER_BACKGROUND_COLOR);
          // 深色背景下设置白色字体以保证可读性
          try { dateHeaderRange.setFontColor(TABLE_STYLES.HEADER_FONT_COLOR); } catch (_fontColorErr) { void _fontColorErr; }
        } catch (_bgError) {
          // 背景色设置不支持
        }

        // 第二行（列标题行）样式
        const columnHeaderRange = sheet.getRange(1, 0, 1, totalCols);
        columnHeaderRange.setFontWeight(TABLE_STYLES.FONT_WEIGHT_BOLD);

        // 将第二行也视为标题行，使用与第一行相同的主题背景色，并设置白色字体
        try {
          columnHeaderRange.setBackgroundColor(TABLE_STYLES.HEADER_BACKGROUND_COLOR);
          try { columnHeaderRange.setFontColor(TABLE_STYLES.HEADER_FONT_COLOR); } catch (_fontColorErr2) { void _fontColorErr2; }
        } catch (_bgError2) {
          // 背景色设置不支持
        }

        // 标题行样式设置完成

      } catch (e) {
        Output.error("[applyTableStyles] 设置标题行样式时出错, error: " + e.message);
      }
    }

    // 设置左侧标题列背景色（从第2行开始，包含“姓名”列头及数据行）
    try {
      if (totalRows > 2) {
        const leftTitleRange = sheet.getRange(2, 0, totalRows - 2, 1);
        leftTitleRange.setBackgroundColor(TABLE_STYLES.LEFT_TITLE_COLUMN_BACKGROUND_COLOR);
        leftTitleRange.setFontWeight(TABLE_STYLES.FONT_WEIGHT_BOLD);
      }
    } catch (_leftColErr) {
      // 背景色设置不支持
    }

    // 设置列宽和格式
    try {
      // 设置姓名列宽度
      sheet.setColumnWidth(0, TABLE_STYLES.NAME_COLUMN_WIDTH);

      // 设置其他列宽度
      for (let i = 1; i < totalCols; i++) {
        const colType = (i - 1) % 6; // 0=任务描述, 1=单项工时, 2=折算工时, 3=行业, 4=行业合计工时, 5=合计工时
        if (colType === 0) {
          sheet.setColumnWidth(i, TABLE_STYLES.TASK_COLUMN_WIDTH); // 任务描述列宽一些
        } else if (colType === 3) {
          sheet.setColumnWidth(i, TABLE_STYLES.INDUSTRY_COLUMN_WIDTH); // 行业列
        } else {
          sheet.setColumnWidth(i, TABLE_STYLES.HOUR_COLUMN_WIDTH);  // 工时相关列
        }
      }

      // 设置工时列的数字格式
      for (let dateIndex = 0; dateIndex < allDates.length; dateIndex++) {
        const baseCol = dateIndex * 6 + 1; // 当日起始列
        const singleHourCol = baseCol + 1;          // 单项工时列
        const convertedHourCol = baseCol + 2;       // 折算工时列
        const industryTotalHourCol = baseCol + 4;   // 行业合计工时列
        const totalHourCol = baseCol + 5;           // 合计工时列

        try {
          // 设置工时列数字格式（保留两位小数）
          const singleHourRange = sheet.getRange(2, singleHourCol, totalRows - 2, 1);
          const convertedHourRange = sheet.getRange(2, convertedHourCol, totalRows - 2, 1);
          const industryTotalHourRange = sheet.getRange(2, industryTotalHourCol, totalRows - 2, 1);
          const totalHourRange = sheet.getRange(2, totalHourCol, totalRows - 2, 1);

          singleHourRange.setNumberFormat(TABLE_STYLES.NUMBER_FORMAT_HOURS);
          convertedHourRange.setNumberFormat(TABLE_STYLES.NUMBER_FORMAT_HOURS);
          industryTotalHourRange.setNumberFormat(TABLE_STYLES.NUMBER_FORMAT_HOURS);
          totalHourRange.setNumberFormat(TABLE_STYLES.NUMBER_FORMAT_HOURS);

        } catch (formatError) {
          void formatError; // 忽略格式设置错误
        }
      }

      // 列宽和格式设置完成

    } catch (_e) {
      // 列宽设置不支持
    }

    // 为合计工时列设置背景色
    try {
      for (let dateIndex = 0; dateIndex < allDates.length; dateIndex++) {
        const totalHourCol = dateIndex * 6 + 6; // 合计工时列索引 (1+5, 7+5, 13+5, ...)
        if (totalHourCol < totalCols) {
          const totalHourColumnRange = sheet.getRange(2, totalHourCol, totalRows - 2, 1);
          totalHourColumnRange.setBackgroundColor('#FFF2CC'); // 浅黄色背景
        }
      }
    } catch (_bgError) {
      // 背景色设置不支持
    }

    // 设置行高自适应
    try {
      for (let i = 0; i < totalRows; i++) {
        sheet.setRowHeight(i, TABLE_STYLES.ROW_HEIGHT);
      }

      // 行高设置完成

    } catch (_e) {
      // 行高设置不支持
    }

    // 为整个表格增加边框
    try {
      const borderRange = sheet.getRange(0, 0, totalRows, totalCols);
      if (borderRange.setBorder) {
        borderRange.setBorder({ top: true, bottom: true, left: true, right: true, vertical: true, horizontal: true }, "#000000", "solid");
      }
    } catch (_borderErr) {
      // 边框设置不支持
    }

    // 表格样式应用完成

  } catch (error) {
    Output.error("[applyTableStyles] 应用表格样式时出错, error: " + error.message);
  }
}

/**
 * 为工作表设置冻结窗格（严格按 DingTalk-Sheet API）
 * 仅使用 setFrozenRowCount / setFrozenColumnCount。
 * @param {Sheet} sheet - 工作表对象
 * @param {number} topRows - 冻结的行数
 * @param {number} leftCols - 冻结的列数
 */
function applyFreezePanes(sheet, topRows, leftCols) {
  const rows = Math.max(0, Number(topRows) || 0);
  const cols = Math.max(0, Number(leftCols) || 0);
  try { if (typeof sheet.setFrozenRowCount === 'function') { sheet.setFrozenRowCount(rows); } } catch (_e1) { void _e1; }
  try { if (typeof sheet.setFrozenColumnCount === 'function') { sheet.setFrozenColumnCount(cols); } } catch (_e2) { void _e2; }
}

// (deprecated) calculateIndustryManpower 已被 calculateIndustryHeadcountAndHours 替代

/**
 * 统计行业维度的“人力(人数)”与“工时(小时)”
 * @param {Array} dailyWorkAnalysis - 每日工作分析数据
 * @returns {Map<string, Map<number, { headcount: number, hours: number }>>}
 */
function calculateIndustryHeadcountAndHours(dailyWorkAnalysis) {
  // 临时结构包含人员集合以便去重
  const tempMap = new Map(); // Map<Industry, Map<DateKey, { people: Set<string>, hours: number }>>

  dailyWorkAnalysis.forEach(day => {
    const dateKey = day.dateSerial;
    day.tasks.forEach(task => {
      if (!task.industry) return;

      if (!tempMap.has(task.industry)) {
        tempMap.set(task.industry, new Map());
      }
      const industryDateMap = tempMap.get(task.industry);

      if (!industryDateMap.has(dateKey)) {
        industryDateMap.set(dateKey, { people: new Set(), hours: 0 });
      }
      const entry = industryDateMap.get(dateKey);

      // 工时累计：保持与历史口径一致，直接相加任务工时
      entry.hours += (parseFloat(task.workHours) || 0);

      // 人员集合：用于统计当天该行业的独立人数
      if (Array.isArray(task.testPersonnel)) {
        task.testPersonnel.forEach(person => {
          const name = String(person || '').trim();
          if (name) entry.people.add(name);
        });
      }
    });
  });

  // 输出结构：去掉集合，仅保留人数与工时
  const result = new Map(); // Map<Industry, Map<DateKey, { headcount, hours }>>
  tempMap.forEach((dateMap, industry) => {
    const outDateMap = new Map();
    dateMap.forEach((entry, dateKey) => {
      outDateMap.set(dateKey, { headcount: entry.people.size, hours: entry.hours });
    });
    result.set(industry, outDateMap);
  });

  return result;
}

/**
 * 生成行业工时日历工作表
 * @param {Map<string, Map<number, { headcount: number, hours: number }>>} industryStats - 行业统计数据
 */
function generateDailyManpowerSheet(industryStats) {
  try {
    // 创建或获取目标工作表
    let sheet = Workbook.getSheet(SHEET_NAMES.DAILY_MANPOWER);
    if (sheet) {
      // 清空现有工作表
      try {
        const clearRange = sheet.getRange("A1:AZ1000");
        clearRange.clear();
      } catch (e) {
        // 忽略清空错误
      }
    } else {
      sheet = Workbook.insertSheet(SHEET_NAMES.DAILY_MANPOWER);
      // 创建新工作表
    }

    if (!sheet) {
      Output.error(`[generateDailyManpowerSheet] 无法创建或获取工作表，跳过生成, sheetName: ${SHEET_NAMES.DAILY_MANPOWER}`);
      return;
    }

    const industries = Array.from(industryStats.keys()).sort();
    const allDatesSet = new Set();
    industryStats.forEach(dateMap => {
      dateMap.forEach((_val, dateKey) => allDatesSet.add(Number(dateKey)));
    });
    const sortedDates = Array.from(allDatesSet).sort((a, b) => a - b); // 直接用序列号排序

    // 第一行：日期（跨两列合并）
    sortedDates.forEach((serial, index) => {
      const colOffset = index * 2 + 1; // 从第2列开始，每个日期占2列
      const dateCell = sheet.getCell(0, colOffset);
      dateCell.setValue(serial);
      dateCell.setNumberFormat('m"/"d');
      const mergeRange = sheet.getRange(0, colOffset, 1, 2);
      try { mergeRange.merge(); } catch (_e) { void _e; }
    });

    // 第二行：左侧行业标题 + 子列标题
    sheet.getCell(1, 0).setValue("行业");
    sortedDates.forEach((_, index) => {
      const colOffset = index * 2 + 1;
      sheet.getCell(1, colOffset).setValue("人力");
      sheet.getCell(1, colOffset + 1).setValue("工时(小时)");
    });

    // 数据区
    let currentRow = 2; // 从第3行开始写数据
    industries.forEach(industry => {
      const dateMap = industryStats.get(industry) || new Map();
      const rowIndex = currentRow++;
      sheet.getCell(rowIndex, 0).setValue(industry);
      sortedDates.forEach((serial, index) => {
        const stat = dateMap.get(serial) || { headcount: 0, hours: 0 };
        const colOffset = index * 2 + 1;
        sheet.getCell(rowIndex, colOffset).setValue(stat.headcount);
        sheet.getCell(rowIndex, colOffset + 1).setValue(stat.hours);
      });
    });

    // 应用样式
    applyDailyManpowerStyles(sheet, sortedDates.length, industries.length + 2);

    // 冻结顶部两行与左侧一列（行业工时日历）
    try { applyFreezePanes(sheet, 2, 1); } catch (_freezeErr) { void _freezeErr; }

    Output.log("[generateDailyManpowerSheet] 行业工时日历生成完成");

  } catch (error) {
    Output.error(`[generateDailyManpowerSheet] 生成工作表时出错, sheetName: ${SHEET_NAMES.DAILY_MANPOWER}, error: ` + error.message);
    if (error.stack) {
      Output.error("[generateDailyManpowerSheet] 错误堆栈, stack: " + error.stack);
    }
  }
}

/**
 * 为行业工时日历工作表应用样式
 * @param {Sheet} sheet - 工作表对象
 * @param {number} dateCount - 日期数量
 * @param {number} totalRows - 总行数
 */
function applyDailyManpowerStyles(sheet, dateCount, totalRows) {
  try {
    const totalCols = dateCount * 2 + 1;

    // 顶部两行标题样式
    const headerRow1 = sheet.getRange(0, 0, 1, totalCols);
    const headerRow2 = sheet.getRange(1, 0, 1, totalCols);
    headerRow1.setFontWeight(TABLE_STYLES.FONT_WEIGHT_BOLD);
    headerRow2.setFontWeight(TABLE_STYLES.FONT_WEIGHT_BOLD);
    try {
      headerRow1.setBackgroundColor(TABLE_STYLES.HEADER_BACKGROUND_COLOR);
      headerRow2.setBackgroundColor(TABLE_STYLES.HEADER_BACKGROUND_COLOR);
      try { headerRow1.setFontColor(TABLE_STYLES.HEADER_FONT_COLOR); } catch (_e0) { void _e0; }
      try { headerRow2.setFontColor(TABLE_STYLES.HEADER_FONT_COLOR); } catch (_e1) { void _e1; }
    } catch (_e2) { void _e2; }

    // 左侧“行业”列背景色（从第3行开始）
    try {
      if (totalRows > 2) {
        const leftTitleRange = sheet.getRange(2, 0, totalRows - 2, 1);
        leftTitleRange.setBackgroundColor(TABLE_STYLES.LEFT_TITLE_COLUMN_BACKGROUND_COLOR);
        leftTitleRange.setFontWeight(TABLE_STYLES.FONT_WEIGHT_BOLD);
      }
    } catch (_leftErr) { void _leftErr; }

    // 行交替底色（仅数据区且不覆盖左侧“行业”列）
    if (totalCols > 1) {
      for (let r = 2; r < totalRows; r++) {
        if ((r - 2) % 2 === 0) {
          try { sheet.getRange(r, 1, 1, totalCols - 1).setBackgroundColor(TABLE_STYLES.ALTERNATE_ROW_BACKGROUND_COLOR); } catch (_e) { void _e; }
        }
      }
    }

    // 列宽：行业列 + 每个日期的两列
    sheet.setColumnWidth(0, TABLE_STYLES.INDUSTRY_COLUMN_WIDTH);
    for (let i = 0; i < dateCount; i++) {
      const headcountCol = i * 2 + 1;
      const hoursCol = i * 2 + 2;
      sheet.setColumnWidth(headcountCol, TABLE_STYLES.HEADCOUNT_COLUMN_WIDTH);
      sheet.setColumnWidth(hoursCol, TABLE_STYLES.HOUR_COLUMN_WIDTH);
    }

    // 数字格式：人力为整数，工时为两位小数
    if (totalRows > 2 && dateCount > 0) {
      for (let i = 0; i < dateCount; i++) {
        const headcountRange = sheet.getRange(2, i * 2 + 1, totalRows - 2, 1);
        const hoursRange = sheet.getRange(2, i * 2 + 2, totalRows - 2, 1);
        try { headcountRange.setNumberFormat('0'); } catch (_e) { void _e; }
        try { hoursRange.setNumberFormat(TABLE_STYLES.NUMBER_FORMAT_HOURS); } catch (_e2) { void _e2; }
      }
    }

    // 行高
    try {
      for (let i = 0; i < totalRows; i++) { sheet.setRowHeight(i, TABLE_STYLES.ROW_HEIGHT); }
    } catch (_eRh) { void _eRh; }

    // 边框
    try {
      const borderRange = sheet.getRange(0, 0, totalRows, totalCols);
      if (borderRange.setBorder) {
        borderRange.setBorder({ top: true, bottom: true, left: true, right: true, vertical: true, horizontal: true }, "#000000", "solid");
      }
    } catch (_eB) { void _eB; }

    // 样式应用完成

  } catch (error) {
    Output.error("[applyDailyManpowerStyles] 应用行业工时日历工作表样式时出错, error: " + error.message);
  }
}

await main()


# 钉钉文档脚本需求文档 - 测试人力计划解析器

## 项目概述

开发一个钉钉文档脚本，用于自动解析"测试人力计划"Excel表格，提取并分析每个人每天的工作内容和工时数据。

## 功能需求

### 1. 数据源

- **输入文件**：Excel工作簿，包含"测试人力计划"工作表
- **备选工作表名称**：
  - "测试人力计划"
  - "【国内货运】所有项目测试计划跟进表"

### 2. 数据结构解析

#### 2.1 表头识别

- **项目_迭代（与RDMS一致）**：项目和迭代信息列
- **事项名称**：具体任务名称列
- **测试人员**：负责测试的人员列
- **日期列**：格式为"6/4"、"6/5"等的日期列

#### 2.2 数据提取规则

- **行业信息**：从第1列（索引0）提取，支持合并单元格处理
- **任务描述**：由"项目_迭代（与RDMS一致）"和"事项名称"拼接而成
  - 格式：`${项目迭代} - ${事项名称}`
- **测试人员**：支持多人，使用逗号、中文逗号、顿号或空格分隔
- **工时数据**：从日期列中提取数值，仅处理大于0的工时

### 3. 输出格式

#### 3.1 主要数据结构

```javascript
{
  "dailyWorkAnalysis": [
    {
      "date": "6/4",
      "tasks": [
        {
          "taskDescription": "项目名称 - 事项名称",
          "testPersonnel": ["测试人员1", "测试人员2"],
          "workHours": 0.2,
          "industry": "行业名称"
        }
      ]
    }
  ]
}
```

#### 3.2 统计信息

- 总天数统计
- 总任务数统计
- 总工时统计
- 人员工时分布统计
- 每日工时分布统计

### 4. 技术实现要求

#### 4.1 平台兼容性

- 基于钉钉文档脚本API开发
- 使用`Workbook`对象获取工作表
- 使用`Output.log()`输出结果

#### 4.2 错误处理

- 工作表不存在时的容错处理
- 空数据行的过滤
- API方法兼容性处理（如`getRowCount()`、`getColumnCount()`）

#### 4.3 数据处理

- 智能表头识别和列映射
- 日期格式解析和排序
- 多人员字符串分割处理
- 数值型工时数据验证

### 5. 脚本特性

#### 5.1 自动化功能

- 自动识别多个可能的工作表名称
- 自动解析表头结构
- 自动过滤无效数据

#### 5.2 输出功能

- JSON格式的结构化数据输出
- 详细的统计分析报告
- 个人任务日历工作表生成
- 每日人力工作表生成（按行业统计）
- 调试信息和执行日志

#### 5.3 容错机制

- 处理不同的API方法可用性
- 跳过空行和无效数据
- 提供详细的错误信息

### 6. 工作表输出

#### 6.1 个人任务日历工作表

- **表格结构**：按人员和日期组织的任务详情
- **列结构**：姓名 | 日期1(任务描述、单项工时、合计工时) | 日期2(...) | ...
- **功能特性**：
  - 支持每人每日多任务显示
  - 自动合并单元格（姓名、合计工时）
  - 表格美化（颜色、字体、边框）

#### 6.2 每日人力工作表

- **表格结构**：按行业统计每日所需人力
- **列结构**：行业 | 日期1 | 日期2 | ... | 合计
- **数据来源**：
  - 行业信息来自第1列（支持合并单元格）
  - 人力需求按行业每日工时累加计算
- **功能特性**：
  - 自动按行业分组统计
  - 显示每日人力需求和总计
  - 表格样式美化

## 使用场景

1. **项目管理**：快速了解测试资源分配情况
2. **工时统计**：按人员和日期统计工作量
3. **进度跟踪**：分析每日测试任务分布
4. **资源优化**：识别工作负载分布不均的情况
5. **行业分析**：了解不同行业的人力需求分布

## 预期效果

- 自动化处理测试人力计划数据
- 提供结构化的工时分析报告
- 减少手动统计工作量
- 提高数据分析效率

## 技术约束

- 必须在钉钉文档环境中运行
- 依赖钉钉文档脚本API
- 需要处理不同版本API的兼容性问题

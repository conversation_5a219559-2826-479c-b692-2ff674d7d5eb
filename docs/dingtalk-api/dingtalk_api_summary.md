# 钉钉文档脚本 API 摘要

基于对钉钉开放平台文档 (`Workbook`, `Sheet`, `Range`) 的分析，以下是对关键 API 的总结：

## 1. Workbook (工作簿)

`Workbook` 是操作整个钉钉表格文档的入口。

### 常用方法

- **`getSheets()`**
  - **说明**: 获取文档中所有的工作表对象。
  - **返回**: `Sheet[]` - Sheet 对象数组。
  - **示例**: `const sheets = Workbook.getSheets();`

- **`getSheet(key)`**
  - **说明**: 根据工作表的名称或 ID 获取特定的 Sheet 对象。
  - **参数**: `key` (string) - 工作表的名称或 ID。
  - **返回**: `Sheet | null` - 找到的 Sheet 对象，未找到则返回 `null`。
  - **示例**: `const sheet = Workbook.getSheet("测试任务日历");`

- **`getActiveSheet()`**
  - **说明**: 获取当前激活（选中）的工作表。
  - **返回**: `Sheet` - 当前激活的 Sheet 对象。
  - **示例**: `const activeSheet = Workbook.getActiveSheet();`

- **`insertSheet(name, index)`**
  - **说明**: 在指定位置插入一个新工作表。
  - **参数**:
    - `name` (string, 可选) - 新工作表的名称。
    - `index` (number, 可选) - 插入位置的索引（从 0 开始）。
  - **返回**: `Sheet` - 新创建的 Sheet 对象。
  - **示例**: `const newSheet = Workbook.insertSheet("个人工时日历(Auto)", 2);`

- **`deleteSheet(name)`**
  - **说明**: 删除指定名称的工作表。
  - **参数**: `name` (string | Sheet) - 要删除的工作表名称或对象。
  - **示例**: `Workbook.deleteSheet("旧的临时表");`

- **`moveSheet(sheet, targetIndex)`**
  - **说明**: 移动一个工作表到新的索引位置。
  - **参数**:
    - `sheet` (Sheet) - 要移动的 Sheet 对象。
    - `targetIndex` (number) - 目标位置的索引。
  - **示例**: `Workbook.moveSheet(sheetToMove, 0);`

- **`getRange(...)`**
  - **说明**: 在当前激活的工作表上获取一个范围 (Range)。此方法是 `getActiveSheet().getRange(...)` 的快捷方式。
  - **重载**:
    - `getRange(a1Notation: string)` - 使用 A1 记号（如 "A1:B2"）。
    - `getRange(row, column, rowCount, columnCount)` - 使用行列索引和行列数。
  - **返回**: `Range` - 获取到的区域对象。
  - **示例**:
    ```javascript
    const range1 = Workbook.getRange("A1:Z1000");
    const range2 = Workbook.getRange(0, 0, 1000, 26); // 与上一行等效
    ```

- **`getActiveCell()`**
  - **说明**: 获取当前激活的单元格。此方法是 `getActiveSheet().getActiveCell()` 的快捷方式。
  - **返回**: `Range` - 代表单个单元格的 Range 对象。

- **`getActiveRange()`**
  - **说明**: 获取激活的选区。如果激活了多个不连续区域，则返回activeCell所在的选区。
  - **返回**: `Range | null` - 当前激活的选区。如果当前选中了图表等，则返回null。

- **`getRangeList(a1Notations)`**
  - **说明**: 获取多个不连续的区域。此方法是 `getActiveSheet().getRangeList(...)` 的快捷方式。
  - **参数**: `a1Notations` (string[]) - A1 记号字符串数组。
  - **返回**: `RangeList` - 代表多个区域的集合。

- **`newFilterCriteriaBuilder()`**
  - **说明**: 创建一个用于构建筛选条件的对象。
  - **返回**: `FilterCriteriaBuilder` - 筛选条件构建器。

## 2. Sheet (工作表)

`Sheet` 代表一个具体的工作表。

### 常用方法

- **`getRange(...)`**
  - **说明**: 在当前工作表上获取一个范围 (Range)。
  - **重载**:
    - `getRange(a1Notation: string)` - 使用 A1 记号（如 "A1:B2"）。
    - `getRange(row, column, rowCount, columnCount)` - 使用行列索引 (从0开始) 和行列数 (从1开始)。
  - **返回**: `Range` - 获取到的区域对象。
  - **示例**:
    ```javascript
    const sheet = Workbook.getSheet("数据源");
    const range1 = sheet.getRange("B2:D10");
    const range2 = sheet.getRange(1, 1, 9, 3); // 与上一行等效 (B2是第2行第2列，即索引1,1)
    ```

- **`getActiveCell()`**
  - **说明**: 获取当前工作表上激活的单元格。
  - **返回**: `Range` - 代表单个单元格的 Range 对象。

- **`getActiveRange()`**
  - **说明**: 获取当前工作表上选中的区域。
  - **返回**: `Range | null` - 当前选中的区域。

- **`getRangeList(a1Notations)`**
  - **说明**: 在当前工作表上获取多个不连续的区域。
  - **参数**: `a1Notations` (string[]) - A1 记号字符串数组。
  - **返回**: `RangeList` - 代表多个区域的集合。

- **`getName()`**
  - **说明**: 获取工作表的名称。
  - **返回**: `string` - 工作表名称。

- **`getCell(row, column)`**
  - **说明**: 获取工作表上单个单元格 (Cell) 对象。这是获取单个单元格进行精细操作（如设置值、格式）的推荐方法。
  - **参数**:
    - `row` (number) - 行索引 (从 0 开始)。
    - `column` (number) - 列索引 (从 0 开始)。
  - **返回**: `Range` - 代表单个单元格的 Range 对象。
  - **示例**: `const cell = sheet.getCell(0, 0); // 获取 A1 单元格`

- **`clear()`**
  - **说明**: 清空整个工作表的内容和格式。
  - **示例**: `sheet.clear();`

- **`setFrozenRowCount(rows)`**
  - **说明**: 设置冻结的行数。
  - **参数**: `rows` (number) - 要冻结的行数。

- **`setFrozenColumnCount(cols)`**
  - **说明**: 设置冻结的列数。
  - **参数**: `cols` (number) - 要冻结的列数。

- **`setColumnWidth(columnIndex, width)`**
  - **说明**: 设置指定列的宽度。
  - **参数**:
    - `columnIndex` (number) - 列索引 (从 0 开始)。
    - `width` (number) - 宽度值（像素）。

- **`setRowHeight(rowIndex, height)`**
  - **说明**: 设置指定行的高度。
  - **参数**:
    - `rowIndex` (number) - 行索引 (从 0 开始)。
    - `height` (number) - 高度值（像素）。

## 3. Range (区域)

`Range` 代表工作表上的一个矩形区域，可以是一个单元格、一行、一列或一个矩形块。

### 常用方法

- **`getValues()`**
  - **说明**: 获取区域内所有单元格的值，返回一个二维数组。
  - **返回**: `any[][]` - 二维数组，第一维是行，第二维是列。
  - **示例**:
    ```javascript
    const range = sheet.getRange(0, 0, 5, 3); // A1:C5
    const values = range.getValues(); // [[A1值, B1值, C1值], [A2值, B2值, C2值], ...]
    ```

- **`setValues(values)`**
  - **说明**: 将二维数组的值写入区域。数组的大小应与区域大小匹配。
  - **参数**:
    - `values` (any[][]) - 要写入的值。
    - `options` (SetValueOptions, 可选) - 设置选项（如 `ignoreEmpty`）。
  - **示例**:
    ```javascript
    const data = [["姓名", "年龄"], ["张三", 25], ["李四", 30]];
    sheet.getRange(0, 0, 3, 2).setValues(data); // 写入 A1:B3
    ```

- **`setValue(value, options)`**
  - **说明**: 将同一个值写入区域内的所有单元格。
  - **参数**:
    - `value` (any) - 要设置的值。
    - `options` (SetValueOptions, 可选) - 设置选项。
  - **示例**:
    ```javascript
    range.setValue("占位符");
    range.setValue(100, { ignoreEmpty: true }); // 忽略空值的设置选项示例（具体选项需查文档）
    ```

- **`clear()`**
  - **说明**: 清空区域内的所有内容和格式。
  - **示例**: `range.clear();`

- **`merge()`**
  - **说明**: 合并区域内的所有单元格。
  - **示例**: `sheet.getRange(0, 0, 1, 5).merge(); // 合并 A1:E1`

- **`getCell(rowOffset, columnOffset)`**
  - **说明**: 获取此区域内相对于区域左上角偏移量的单元格。
  - **参数**:
    - `rowOffset` (number) - 行偏移量 (从 0 开始)。
    - `columnOffset` (number) - 列偏移量 (从 0 开始)。
  - **返回**: `Range` - 代表单个单元格的 Range 对象。
  - **示例**:
    ```javascript
    const range = sheet.getRange(5, 2, 10, 5); // 区域是 C6:G15
    const cellInThisRange = range.getCell(0, 0); // 这将是 C6 单元格
    ```

### 单元格属性设置方法 (应用于整个 Range)

以下方法用于设置区域内所有单元格的样式属性：

- **`setValue(value)`**: 设置单元格的值。
- **`setBackgroundColor(color)`**: 设置背景色 (如 `'#FFFF00'`)。
- **`setFontColor(color)`**: 设置字体颜色。
- **`setFontWeight(weight)`**: 设置字体粗细 (如 `'bold'`)。
- **`setFontFamily(fontFamily)`**: 设置字体 (如 `'Arial'`)。
- **`setFontSize(fontSize)`**: 设置字体大小。
- **`setHorizontalAlignment(alignment)`**: 设置水平对齐方式 (如 `'center'`)。
- **`setVerticalAlignment(alignment)`**: 设置垂直对齐方式 (如 `'middle'`)。
- **`setNumberFormat(format)`**: 设置数字格式 (如 `'0.00'`, `'m/d/yyyy'`)。

## 4. 注意事项与兼容性

- **API 可用性**: 钉钉文档脚本的 API 可能存在版本差异或部分功能未完全支持。在实际开发中，建议对关键的 API 调用进行错误处理。
- **索引**: 行和列的索引通常从 **0** 开始。
- **区域大小**: 使用 `getRange(row, col, rowCount, colCount)` 时，`rowCount` 和 `colCount` 是从 1 开始计算的区域大小。
- **Cell**: 实际上，钉钉文档脚本 API 中并没有独立的 `Cell` 对象，对单个单元格的操作是通过获取一个只包含该单元格的 `Range` 对象来实现的（例如通过 `sheet.getCell(row, col)`）。
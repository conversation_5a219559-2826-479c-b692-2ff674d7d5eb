# Sheet (工作表)

`Sheet` 代表一个具体的工作表。

## 常用方法

### `getRange(...)`
- **说明**: 在当前工作表上获取一个范围 (Range)。
- **重载**:
  - `getRange(a1Notation: string)` - 使用 A1 记号（如 "A1:B2"）。
  - `getRange(row, column, rowCount, columnCount)` - 使用行列索引 (从0开始) 和行列数 (从1开始)。
- **返回**: `Range` - 获取到的区域对象。
- **示例**:
  ```javascript
  const sheet = Workbook.getSheet("数据源");
  const range1 = sheet.getRange("B2:D10");
  const range2 = sheet.getRange(1, 1, 9, 3); // 与上一行等效 (B2是第2行第2列，即索引1,1)
  ```

### `getActiveCell()`
- **说明**: 获取当前工作表上激活的单元格。
- **返回**: `Range` - 代表单个单元格的 Range 对象。

### `getActiveRange()`
- **说明**: 获取当前工作表上选中的区域。
- **返回**: `Range | null` - 当前选中的区域。

### `getRangeList(a1Notations)`
- **说明**: 在当前工作表上获取多个不连续的区域。
- **参数**: `a1Notations` (string[]) - A1 记号字符串数组。
- **返回**: `RangeList` - 代表多个区域的集合。

### `getName()`
- **说明**: 获取工作表的名称。
- **返回**: `string` - 工作表名称。

### `getCell(row, column)`
- **说明**: 获取工作表上单个单元格 (Cell) 对象。这是获取单个单元格进行精细操作（如设置值、格式）的推荐方法。
- **参数**:
  - `row` (number) - 行索引 (从 0 开始)。
  - `column` (number) - 列索引 (从 0 开始)。
- **返回**: `Range` - 代表单个单元格的 Range 对象。
- **示例**: `const cell = sheet.getCell(0, 0); // 获取 A1 单元格`

### `clear()`
- **说明**: 清空整个工作表的内容和格式。
- **示例**: `sheet.clear();`

### `setFrozenRowCount(rows)`
- **说明**: 设置冻结的行数。
- **参数**: `rows` (number) - 要冻结的行数。

### `setFrozenColumnCount(cols)`
- **说明**: 设置冻结的列数。
- **参数**: `cols` (number) - 要冻结的列数。

### `setColumnWidth(columnIndex, width)`
- **说明**: 设置指定列的宽度。
- **参数**:
  - `columnIndex` (number) - 列索引 (从 0 开始)。
  - `width` (number) - 宽度值（像素）。

### `setRowHeight(rowIndex, height)`
- **说明**: 设置指定行的高度。
- **参数**:
  - `rowIndex` (number) - 行索引 (从 0 开始)。
  - `height` (number) - 高度值（像素）。

### `activate()`
- **说明**: 激活自身。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getSheet('Sheet1').activate();`

### `getId()`
- **说明**: 获取ID。
- **返回**: `string` - 工作表的ID。
- **示例**: `const sheetId = Workbook.getActiveSheet().getId();`

### `getIndex()`
- **说明**: 获取工作表的索引。
- **返回**: `number` - 工作表的索引。
- **示例**: `const index = Workbook.getActiveSheet().getIndex();`

### `setTabColor(color)`
- **说明**: 设置Sheet标签颜色。
- **参数**: `color` (string | null) - 颜色。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setTabColor('yellow');`

### `getTabColor()`
- **说明**: 获取Sheet标签颜色。
- **返回**: `Color | null` - 颜色。null代表没有设置颜色。
- **示例**: `const tabColor = Workbook.getActiveSheet().getTabColor();`

### `setName(name)`
- **说明**: 设置工作表的名字。
- **参数**: `name` (string) - 新名字。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setName('New name');`

### `getVisibility()`
- **说明**: 获取可见性。
- **返回**: `'visible' | 'hidden'` - 可见性。
- **示例**: `const visibility = Workbook.getActiveSheet().getVisibility();`

### `getGridlinesVisibility()`
- **说明**: 获取网格线可见性。
- **返回**: `'visible' | 'hidden'` - 可见性。
- **示例**: `const gridlinesVisibility = Workbook.getActiveSheet().getGridlinesVisibility();`

### `setGridlinesVisibility(visibility)`
- **说明**: 设置网格线可见性。
- **参数**: `visibility` (`'visible' | 'hidden'`) - 可见性。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setGridlinesVisibility('hidden');`

### `getFrozenColumnCount()`
- **说明**: 获取冻结的列。
- **返回**: `number` - 列数，从1开始，0表示不冻结。
- **示例**: `const frozenColumnCount = Workbook.getActiveSheet().getFrozenColumnCount();`

### `getFrozenRowCount()`
- **说明**: 获取冻结的行。
- **返回**: `number` - 行数，从1开始，0表示不冻结。
- **示例**: `const frozenRowCount = Workbook.getActiveSheet().getFrozenRowCount();`

### `setFrozenColumnCount(columnCount)`
- **说明**: 设置冻结列。
- **参数**: `columnCount` (number) - 列数，从 1 开始，0表示不冻结。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setFrozenColumnCount(5);`

### `setFrozenRowCount(rowCount)`
- **说明**: 设置冻结行。
- **参数**: `rowCount` (number) - 行数，从 1 开始。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setFrozenRowCount(5);`

### `getColumnCount()`
- **说明**: 获取当前Sheet的列数。
- **返回**: `number` - 列数。
- **示例**: `const columnCount = Workbook.getActiveSheet().getColumnCount();`

### `getRowCount()`
- **说明**: 获取当前Sheet的行数。
- **返回**: `number` - 行数。
- **示例**: `const rowCount = Workbook.getActiveSheet().getRowCount();`

### `setActiveRange(a1Notation)`
- **说明**: 激活选区。
- **参数**: `a1Notation` (string) - 选区地址。
- **返回**: `Range` - 激活的选区。
- **示例**: `Workbook.getActiveSheet().setActiveRange("A1:C10");`

### `setActiveRange(range)`
- **说明**: 激活选区。
- **参数**: `range` (Range) - 选区。
- **返回**: `Range` - 激活的选区。
- **示例**:
  ```javascript
  const range = Workbook.getRange(0, 0, 2, 2);
  Workbook.getActiveSheet().setActiveRange(range);
  ```

### `getActiveRange()`
- **说明**: 获取 activeCell 所在的选区。
- **返回**: `Range | null` - activeCell 所在的选区。
- **示例**: `const range = Workbook.getActiveSheet().getActiveRange();`

### `getEntireSheet()`
- **说明**: 获取整表区域。
- **返回**: `Range` - 整表区域。
- **示例**: `const range = Workbook.getActiveSheet().getEntireSheet();`

### `getEntireRows(row, rowCount)`
- **说明**: 获取整行。
- **参数**:
  - `row` (number) - 起始行。
  - `rowCount` (number, 可选) - 行数。
- **返回**: `Range | null` - 整行区域。
- **示例**: `const range = Workbook.getActiveSheet().getEntireRows(0, 3);`

### `getEntireColumns(column, columnCount)`
- **说明**: 获取整列。
- **参数**:
  - `column` (number) - 起始列。
  - `columnCount` (number, 可选) - 列数。
- **返回**: `Range | null` - 整列区域。
- **示例**: `const range = Workbook.getActiveSheet().getEntireColumns(0, 3);`

### `setActiveRangeList(a1Notations)`
- **说明**: 激活多选区。
- **参数**: `a1Notations` (string[]) - 选区地址数组。
- **返回**: `RangeList` - 多选区。
- **示例**: `const rangeList = Workbook.getActiveSheet().setActiveRangeList(['A1:C10', 'D2:E5']);`

### `setActiveRangeList(rangeList)`
- **说明**: 激活多选区。
- **参数**: `rangeList` (RangeList) - 多选区对象。
- **返回**: `RangeList` - 多选区。
- **示例**:
  ```javascript
  const rangeList = Workbook.getActiveSheet().getRangeList(['A1:C10', 'D2:E5']);
  Workbook.getActiveSheet().setActiveRangeList(rangeList);
  ```

### `getActiveRangeList()`
- **说明**: 获取激活的多选区。
- **返回**: `RangeList` - 多选区。
- **示例**: `const rangeList = Workbook.getActiveSheet().getActiveRangeList();`

### `getRangeList(a1Notations)`
- **说明**: 获取多选区。
- **参数**: `a1Notations` (string[]) - 选区地址数组。
- **返回**: `RangeList` - 多选区。
- **示例**: `const rangeList = Workbook.getActiveSheet().getRangeList(['A1:C10', 'D2:E5']);`

### `getActiveCell()`
- **说明**: 获取active cell。
- **返回**: `Range` - active cell。
- **示例**: `const range = Workbook.getActiveSheet().getActiveCell();`

### `getCell(row, column)`
- **说明**: 获取单元格。
- **参数**:
  - `row` (number) - 行。
  - `column` (number) - 列。
- **返回**: `Range` - 单元格。
- **示例**: `const range = Workbook.getActiveSheet().getCell(0, 0);`

### `setRowsVisibility(row, rowCount, visibility)`
- **说明**: 显示/隐藏多行。
- **参数**:
  - `row` (number) - 行。
  - `rowCount` (number) - 行数。
  - `visibility` (`'visible' | 'hidden'`) - 可见性。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setRowsVisibility(0, 1, 'hidden'); //隐藏第一行`

### `setRowVisibility(row, visibility)`
- **说明**: 显示/隐藏单行。
- **参数**:
  - `row` (number) - 行。
  - `visibility` (`'visible' | 'hidden'`) - 可见性。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setRowVisibility(1, 'hidden'); //隐藏第二行`

### `setColumnsVisibility(column, columnCount, visibility)`
- **说明**: 显示/隐藏多列。
- **参数**:
  - `column` (number) - 列。
  - `columnCount` (number) - 列数。
  - `visibility` (`'visible' | 'hidden'`) - 可见性。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setColumnsVisibility(0, 1, 'hidden'); //隐藏第一列`

### `setColumnVisibility(column, visibility)`
- **说明**: 显示/隐藏单列。
- **参数**:
  - `column` (number) - 列。
  - `visibility` (`'visible' | 'hidden'`) - 可见性。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setColumnVisibility(1, 'hidden'); //隐藏第二列`

### `setRowsHeight(row, rowCount, height)`
- **说明**: 设置多行的高度。
- **参数**:
  - `row` (number) - 行。
  - `rowCount` (number) - 行数。
  - `height` (number) - 高度。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setRowsHeight(0, 3, 50); //设置第一行到第三行，高度为50`

### `setRowHeight(row, height)`
- **说明**: 设置单行的高度。
- **参数**:
  - `row` (number) - 行。
  - `height` (number) - 高度。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setRowHeight(0, 50);`

### `getRowHeight(row)`
- **说明**: 获取行高。
- **参数**: `row` (number) - 行。
- **返回**: `number` - 行高。
- **示例**: `const height = Workbook.getActiveSheet().getRowHeight(0);`

### `autofitRows(row, rowCount)`
- **说明**: 自适应行高。
- **参数**:
  - `row` (number) - 行。
  - `rowCount` (number) - 行数。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().autofitRows(0, 3);`

### `autofitRow(row)`
- **说明**: 自适应行高。
- **参数**: `row` (number) - 行。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().autofitRow(0);`

### `setColumnsWidth(column, columnCount, width)`
- **说明**: 设置列宽。
- **参数**:
  - `column` (number) - 列。
  - `columnCount` (number) - 列数。
  - `width` (number) - 宽度。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setColumnsWidth(0, 3, 200); //设置A到C列，宽度为200`

### `setColumnWidth(column, width)`
- **说明**: 设置列宽。
- **参数**:
  - `column` (number) - 列。
  - `width` (number) - 宽度。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().setColumnWidth(0, 200);`

### `getColumnWidth(column)`
- **说明**: 获取列宽。
- **参数**: `column` (number) - 列。
- **返回**: `number` - 列宽。
- **示例**: `const width = Workbook.getActiveSheet().getColumnWidth(0);`

### `autofitColumns(column, columnCount)`
- **说明**: 自适应列宽。
- **参数**:
  - `column` (number) - 列。
  - `columnCount` (number) - 列数。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().autofitColumns(0, 3);`

### `autofitColumn(column)`
- **说明**: 自适应列宽。
- **参数**: `column` (number) - 列。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().autofitColumn(0);`

### `insertRowBefore(row)`
- **说明**: 在 row 行之前插入 1 行。
- **参数**: `row` (number) - 行。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().insertRowBefore(0);`

### `insertRowsBefore(row, rowCount)`
- **说明**: 在 row 行之前插入 n 行。
- **参数**:
  - `row` (number) - 行。
  - `rowCount` (number) - 行数。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().insertRowsBefore(0, 10);`

### `insertRowAfter(row)`
- **说明**: 在 row 行之后插入 1 行。
- **参数**: `row` (number) - 行。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().insertRowAfter(0);`

### `insertRowsAfter(row, rowCount)`
- **说明**: 在 row 行之后插入 n 行。
- **参数**:
  - `row` (number) - 行。
  - `rowCount` (number) - 行数。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().insertRowsAfter(0, 10);`

### `insertColumnBefore(column)`
- **说明**: 在 column 列之前插入 1 列。
- **参数**: `column` (number) - 列。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().insertColumnBefore(0);`

### `insertColumnsBefore(column, columnCount)`
- **说明**: 在 column 列之前插入 n 列。
- **参数**:
  - `column` (number) - 列。
  - `columnCount` (number) - 列数。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().insertColumnsBefore(0, 10);`

### `insertColumnAfter(column)`
- **说明**: 在 column 列之后插入 1 列。
- **参数**: `column` (number) - 列。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().insertColumnAfter(0);`

### `insertColumnsAfter(column, columnCount)`
- **说明**: 在 column 列之后插入 n 列。
- **参数**:
  - `column` (number) - 列。
  - `columnCount` (number) - 列数。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().insertColumnsAfter(0, 10);`

### `deleteRows(row, rowCount)`
- **说明**: 删除行。
- **参数**:
  - `row` (number) - 行。
  - `rowCount` (number) - 行数。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().deleteRows(0, 10);`

### `deleteRow(row)`
- **说明**: 删除行。
- **参数**: `row` (number) - 行。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().deleteRow(0);`

### `deleteColumns(column, columnCount)`
- **说明**: 删除列。
- **参数**:
  - `column` (number) - 列。
  - `columnCount` (number) - 列数。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().deleteColumns(0, 10);`

### `deleteColumn(column)`
- **说明**: 删除列。
- **参数**: `column` (number) - 列。
- **返回**: `Sheet` - 当前Sheet实例。
- **示例**: `Workbook.getActiveSheet().deleteColumn(0);`

### `findAll(text, findOptions)`
- **说明**: 根据指定的条件查找匹配给定字符串的所有Range。
- **参数**:
  - `text` (string) - 要查找的字符串。
  - `findOptions` (SearchOptions, 可选) - 其他搜索条件，包括搜索是否需要匹配整个单元格、区分大小写、是否使用正则、是否匹配公式。
- **返回**: `Range[]` - 所有匹配到的区域的数组。
- **示例**:
  ```javascript
  const ranges = sheet.findAll('str');
  const ranges = sheet.findAll('str', {
    matchEntireCell: true,
    matchCase: true,
  });
  ```

### `replaceAll(text, replaceText, replaceOptions)`
- **说明**: 根据当前工作表中指定的条件查找并替换给定的字符串。
- **参数**:
  - `text` (string) - 要查找的字符串。
  - `replaceText` (string) - 替换原始字符串的字符串。
  - `replaceOptions` (SearchOptions, 可选) - 其他替换条件，包括是否需要匹配整个单元格、区分大小写、是否使用正则、是否匹配公式。
- **返回**: `number` - 执行的替换数，可能与匹配单元格的数目不同。
- **示例**:
  ```javascript
  const replaceCount = sheet.replaceAll('str', 'otherStr');
  const replaceCount = sheet.replaceAll('str', 'otherStr', { matchEntireCell: true });
  ```

### `filter(range)`
- **说明**: 创建Filter。
- **参数**: `range` (Range) - 区域。
- **返回**: `Filter` - Filter 实例。
- **示例**:
  ```javascript
  const range = sheet.getRange('A1:C5');
  sheet.filter(range);
  ```

### `getFilter()`
- **说明**: 获取当前sheet上的filter实例。
- **返回**: `Filter | null` - Filter 实例。
- **示例**: `const filter = sheet.getFilter();`

### `deleteFilter()`
- **说明**: 删除当前sheet上的filter。
- **返回**: `void`
- **示例**: `sheet.deleteFilter();`
# Range (区域)

`Range` 代表工作表上的一个矩形区域，可以是一个单元格、一行、一列或一个矩形块。

## 常用方法

### `getValues()`
- **说明**: 获取区域内所有单元格的值，返回一个二维数组。
- **返回**: `any[][]` - 二维数组，第一维是行，第二维是列。
- **示例**:
  ```javascript
  const range = sheet.getRange(0, 0, 5, 3); // A1:C5
  const values = range.getValues(); // [[A1值, B1值, C1值], [A2值, B2值, C2值], ...]
  ```

### `setValues(values, options)`
- **说明**: 将二维数组的值写入区域。数组的大小应与区域大小匹配。
- **参数**:
  - `values` (any[][]) - 要写入的值。
  - `options` (SetValueOptions, 可选) - 设置选项（如 `ignoreEmpty`）。
- **示例**:
  ```javascript
  const data = [["姓名", "年龄"], ["张三", 25], ["李四", 30]];
  sheet.getRange(0, 0, 3, 2).setValues(data); // 写入 A1:B3
  ```

### `setValue(value, options)`
- **说明**: 将同一个值写入区域内的所有单元格。
- **参数**:
  - `value` (any) - 要设置的值。
  - `options` (SetValueOptions, 可选) - 设置选项。
- **示例**:
  ```javascript
  range.setValue("占位符");
  range.setValue(100, { ignoreEmpty: true }); // 忽略空值的设置选项示例（具体选项需查文档）
  ```

### `clear()`
- **说明**: 清空区域内的所有内容和格式。
- **示例**: `range.clear();`

### `merge()`
- **说明**: 合并区域内的所有单元格。
- **示例**: `sheet.getRange(0, 0, 1, 5).merge(); // 合并 A1:E1`

### `unmerge()`
- **说明**: 取消合并单元格。
- **示例**: `range.unmerge();`

### `getCell(rowOffset, columnOffset)`
- **说明**: 获取此区域内相对于区域左上角偏移量的单元格。
- **参数**:
  - `rowOffset` (number) - 行偏移量 (从 0 开始)。
  - `columnOffset` (number) - 列偏移量 (从 0 开始)。
- **返回**: `Range` - 代表单个单元格的 Range 对象。
- **示例**:
  ```javascript
  const range = sheet.getRange(5, 2, 10, 5); // 区域是 C6:G15
  const cellInThisRange = range.getCell(0, 0); // 这将是 C6 单元格
  ```

### `isEntireSheet()`
- **说明**: 是否是整个工作表区域。
- **返回**: `boolean` - 是否是整个工作表区域。

### `isEntireRow()`
- **说明**: 是否是整行。
- **返回**: `boolean` - 是否是整行。

### `isEntireColumn()`
- **说明**: 是否是整列。
- **返回**: `boolean` - 是否是整列。

### `isCell()`
- **说明**: 是否是一个单元格。
- **返回**: `boolean` - 是否是一个单元格。

### `activate()`
- **说明**: 激活选区。
- **返回**: `Range` - 区域。

### `clearFormat()`
- **说明**: 清除样式。
- **返回**: `Range` - 区域。

### `clearData()`
- **说明**: 清除数据。
- **返回**: `Range` - 区域。

### `setBackgroundColor(backgroundColor)`
- **说明**: 设置选区单元格背景颜色。
- **参数**: `backgroundColor` (string | null) - 背景颜色, null 表示清除。使用十六进制字符串形式表示颜色，例如 '#ff0000' 表示红色。
- **返回**: `Range` - 区域。

### `getBackgroundColor()`
- **说明**: 获取左上角单元格背景色。
- **返回**: `Color` - 背景颜色。

### `getBackgroundColors()`
- **说明**: 获取区域单元格的背景色。
- **返回**: `Array<Array<Color>>` - 背景颜色，二维数组。

### `setFontColor(fontColor)`
- **说明**: 设置字体颜色。
- **参数**: `fontColor` (string | null) - 字体颜色, null 表示清除。使用十六进制字符串形式表示颜色，例如 '#ff0000' 表示红色。
- **返回**: `Range` - 区域。

### `getFontColor()`
- **说明**: 获取左上角单元格字体颜色。
- **返回**: `Color` - 字体颜色。

### `getFontColors()`
- **说明**: 获取区域单元格字体颜色。
- **返回**: `Array<Array<Color>>` - 字体颜色。

### `setFontFamily(fontFamily)`
- **说明**: 设置字体。
- **参数**: `fontFamily` (string | null) - 字体, null 表示清除。
- **返回**: `Range` - 区域。

### `getFontFamily()`
- **说明**: 获取左上角单元格字体。
- **返回**: `string` - 字体。

### `getFontFamilies()`
- **说明**: 获取区域单元格字体。
- **返回**: `Array<Array<string>>` - 字体。

### `setFontSize(fontSize)`
- **说明**: 设置字体大小。
- **参数**: `fontSize` (number | null) - 字体大小, null 表示清除。
- **返回**: `Range` - 区域。

### `getFontSize()`
- **说明**: 获取左上角单元格字体大小。
- **返回**: `number` - 字体大小。

### `getFontSizes()`
- **说明**: 获取区域单元格字体大小。
- **返回**: `Array<Array<number>>` - 字体大小。

### `setFontWeight(fontWeight)`
- **说明**: 设置字体粗体。
- **参数**: `fontWeight` (`'bold' | 'normal' | null`) - 字体粗细, null 表示清除。
- **返回**: `Range` - 区域。

### `getFontWeight()`
- **说明**: 获取左上角单元格字体粗体。
- **返回**: `'bold' | 'normal'` - 字体粗体。

### `getFontWeights()`
- **说明**: 获取区域单元格字体粗体。
- **返回**: `Array<Array<'bold' | 'normal'>>` - 字体粗体。

### `setFontStyle(fontStyle)`
- **说明**: 设置字体样式。
- **参数**: `fontStyle` (`'italic' | 'normal' | null`) - 字体样式, null 表示清除。
- **返回**: `Range` - 区域。

### `getFontStyle()`
- **说明**: 获取左上角单元格字体样式。
- **返回**: `'italic' | 'normal'` - 字体样式。

### `getFontStyles()`
- **说明**: 获取区域单元格字体样式。
- **返回**: `Array<Array<'italic' | 'normal'>>` - 字体样式。

### `setIndent(indent)`
- **说明**: 设置缩进。
- **参数**: `indent` (number | null) - 缩进，必须为正整数，null 表示清除。
- **返回**: `Range` - 区域。

### `getIndent()`
- **说明**: 获取左上角单元格缩进。
- **返回**: `number` - 缩进。

### `getIndents()`
- **说明**: 获取区域单元格缩进。
- **返回**: `Array<Array<number>>` - 缩进。

### `setHorizontalAlignment(alignment)`
- **说明**: 设置水平对齐方式。
- **参数**: `alignment` (`'left' | 'center' | 'right' | 'general' | null`) - 水平对齐方式, null 表示清除。
- **返回**: `Range` - 区域。

### `getHorizontalAlignment()`
- **说明**: 获取左上角单元格水平对齐方式。
- **返回**: `'left' | 'center' | 'right' | 'general'` - 水平对齐方式。

### `getHorizontalAlignments()`
- **说明**: 获取区域单元格水平对齐方式。
- **返回**: `Array<Array<'left' | 'center' | 'right' | 'general'>>` - 水平对齐方式。

### `setVerticalAlignment(alignment)`
- **说明**: 设置垂直对齐方式。
- **参数**: `alignment` (`'top' | 'middle' | 'bottom' | null`) - 垂直对齐方式, null 表示清除。
- **返回**: `Range` - 区域。

### `getVerticalAlignment()`
- **说明**: 获取左上角单元格垂直对齐方式。
- **返回**: `'top' | 'middle' | 'bottom'` - 垂直对齐方式。

### `getVerticalAlignments()`
- **说明**: 获取区域单元格垂直对齐方式。
- **返回**: `Array<Array<'top' | 'middle' | 'bottom'>>` - 垂直对齐方式。

### `setWordWrap(wordWrap)`
- **说明**: 设置换行方式。
- **参数**: `wordWrap` (`'overflow' | 'clip' | 'autoWrap' | null`) - 换行方式, null 表示清除。
- **返回**: `Range` - 区域。

### `getWordWrap()`
- **说明**: 获取左上角单元格换行方式。
- **返回**: `'overflow' | 'clip' | 'autoWrap'` - 换行方式。

### `getWordWraps()`
- **说明**: 获取区域单元格换行方式。
- **返回**: `Array<Array<'overflow' | 'clip' | 'autoWrap'>>` - 换行方式。

### `setBorder(null)`
- **说明**: 清除边框。
- **返回**: `Range` - 区域。

### `setBorder(type, color, style)`
- **说明**: 设置边框。
- **参数**:
  - `type` (BorderType) - 边框类型。
  - `color` (string) - 边框颜色。
  - `style` (`'none' | 'dotted' | 'dashed' | 'solid' | 'medium' | 'thick' | 'double' | 'hair' | 'dashDotDot' | 'dashDot' | 'mediumDashDotDot' | 'slantDashDot' | 'mediumDashDot' | 'mediumDashed'`) - 边框样式。
- **返回**: `Range` - 区域。

### `decreaseDecimal()`
- **说明**: 减少小数位，如果 activeCell 在 Range 内，以 activeCell 为参照，否则以左上角单元格为参照。
- **返回**: `Range` - 区域。

### `increaseDecimal()`
- **说明**: 减少小数位，如果 activeCell 在 Range 内，以 activeCell 为参照，否则以左上角单元格为参照。
- **返回**: `Range` - 区域。

### `setNumberFormat(numberFormat)`
- **说明**: 设置数字格式。
- **参数**: `numberFormat` (string) - 数字格式，详情参考[数字格式](https://open.dingtalk.com/document/orgapp-client/number-format#topic-2193105)。
- **返回**: `Range` - 区域。

### `getNumberFormat()`
- **说明**: 获取左上角单元格数字格式。
- **返回**: `string` - 数字格式。

### `getNumberFormats()`
- **说明**: 获取区域单元格数字格式。
- **返回**: `Array<Array<string>>` - 数字格式。

### `getFormula()`
- **说明**: 获取左上角单元格公式表达式。
- **返回**: `string` - 公式表达式，无公式为空字符串。

### `getFormulas()`
- **说明**: 获取区域单元格公式表达式。
- **返回**: `Array<Array<string>>` - 公式表达式，无公式为空字符串。

### `getDisplayValue()`
- **说明**: 获取左上角单元格显示值。显示值指基于数字格式等计算后的值。
- **返回**: `string` - 单元格显示值。

### `getDisplayValues()`
- **说明**: 获取区域单元格显示值。显示值指基于数字格式等计算后的值。
- **返回**: `Array<Array<string>>` - 单元格显示值。

### `getValue()`
- **说明**: 获取左上角单元格值。
- **返回**: `string | number | boolean | null` - 单元格值。

### `getValues()`
- **说明**: 获取区域单元格值。
- **返回**: `Array<Array<string | number | boolean | null>>` - 单元格值。

### `getMergedRanges()`
- **说明**: 获取合并单元格区域。
- **返回**: `Range[]` - 合并单元格区域。

### `getA1Notation()`
- **说明**: 获取区域地址。
- **返回**: `string` - 区域字符串地址。

### `getRow()`
- **说明**: 获取从0开始的行号。
- **返回**: `number` - 行。

### `getColumn()`
- **说明**: 获取从0开始的列号。
- **返回**: `number` - 列。

### `getRowCount()`
- **说明**: 获取行数。
- **返回**: `number` - 行数。

### `getColumnCount()`
- **说明**: 获取列数。
- **返回**: `number` - 列数。

### `getHyperlink()`
- **说明**: 获取单元格链接。
- **返回**: `Hyperlink | null` - 单元格链接, null 表示无链接。

### `sort(field)`
- **说明**: 排序当前区域。
- **参数**: `field` (SortField | number) - 排序规则 ｜ 相对于当前 Range 首列的偏移量。
- **返回**: `Range` - 区域。

### `find(text, findOptions)`
- **说明**: 根据指定的条件查找匹配给定字符串的第一个Range。
- **参数**:
  - `text` (string) - 要查找的字符串。
  - `findOptions` (SearchOptions, 可选) - 其他搜索条件，包括搜索是否需要匹配整个单元格、区分大小写、是否使用正则表达式、是否匹配公式。
- **返回**: `Range` - 区域。

### `findNext(text, findOptions)`
- **说明**: 根据指定的条件查找匹配给定字符串下一个Range，搜索范围为以当前Range左上角的Cell之后开始的整个工作表。
- **参数**:
  - `text` (string) - 要查找的字符串。
  - `findOptions` (SearchOptions, 可选) - 其他搜索条件，包括搜索是否需要匹配整个单元格、区分大小写、是否使用正则、是否匹配公式。
- **返回**: `Range` - 区域。

### `findPrevious(text, findOptions)`
- **说明**: 根据指定的条件查找匹配给定字符串上一个Range，搜索范围为以当前Range左上角的Cell之前的整个工作表。
- **参数**:
  - `text` (string) - 要查找的字符串。
  - `findOptions` (SearchOptions, 可选) - 其他搜索条件，包括搜索是否需要匹配整个单元格、区分大小写、是否使用正则、是否匹配公式。
- **返回**: `Range` - 区域。

### `replaceAll(text, replaceText, replaceOptions)`
- **说明**: 根据指定的条件查找并替换给定的字符串。
- **参数**:
  - `text` (string) - 要查找的字符串。
  - `replaceText` (string) - 替换原始字符串的字符串。
  - `replaceOptions` (SearchOptions, 可选) - 其他替换条件，包括是否需要匹配整个单元格、区分大小写、是否使用正则、是否匹配公式。
- **返回**: `number` - 执行的替换数，可能与匹配单元格的数目不同。

### `insertCheckboxes()`
- **说明**: 插入复选框。
- **返回**: `Range` - Range。

### `deleteCheckboxes()`
- **说明**: 清除复选框，value 不会清除。
- **返回**: `Range` - Range。

### `getCheckedState()`
- **说明**: 返回左上角单元格是否勾选，null表示单元格不是复选框。
- **返回**: `boolean | null` - 是否勾选。

### `getCheckedStates()`
- **说明**: 返回区域单元格是否勾选，null表示单元格不是复选框。
- **返回**: `Array<Array<boolean | null>>` - 区域单元格勾选的状态。

### `setCheckedState(checked)`
- **说明**: 勾选/取消勾选复选框。
- **参数**: `checked` (boolean) - 是否勾选。
- **返回**: `Range` - Range。

### `setCheckedStates(checkeds)`
- **说明**: 勾选/取消勾选复选框。
- **参数**: `checkeds` (Array<Array<boolean | null>>) - 是否勾选，null 表示跳过。
- **返回**: `Range` - Range。

### `insertDropdownLists(options)`
- **说明**: 设置下拉框。
- **参数**: `options` (DropdownListOption[]) - 下拉选项。
- **返回**: `Range` - Range。

### `deleteDropdownLists()`
- **说明**: 清除下拉框，value 不会清除。
- **返回**: `Range` - Range。
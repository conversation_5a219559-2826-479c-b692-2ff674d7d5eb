# Workbook (工作簿)

`Workbook` 是操作整个钉钉表格文档的入口。

## 常用方法

### `getSheets()`

- **说明**: 获取文档中所有的工作表对象。
- **返回**: `Sheet[]` - Sheet 对象数组。
- **示例**: `const sheets = Workbook.getSheets();`

### `getSheet(key)`

- **说明**: 根据工作表的名称或 ID 获取特定的 Sheet 对象。
- **参数**: `key` (string) - 工作表的名称或 ID。
- **返回**: `Sheet | null` - 找到的 Sheet 对象，未找到则返回 `null`。
- **示例**: `const sheet = Workbook.getSheet("测试任务日历");`

### `getActiveSheet()`

- **说明**: 获取当前激活（选中）的工作表。
- **返回**: `Sheet` - 当前激活的 Sheet 对象。
- **示例**: `const activeSheet = Workbook.getActiveSheet();`

### `insertSheet(name, index)`

- **说明**: 在指定位置插入一个新工作表。
- **参数**:
  - `name` (string, 可选) - 新工作表的名称。
  - `index` (number, 可选) - 插入位置的索引（从 0 开始）。
- **返回**: `Sheet` - 新创建的 Sheet 对象。
- **示例**: `const newSheet = Workbook.insertSheet("个人工时日历(Auto)", 2);`

### `deleteSheet(name)`

- **说明**: 删除指定名称的工作表。
- **参数**: `name` (string | Sheet) - 要删除的工作表名称或对象。
- **示例**: `Workbook.deleteSheet("旧的临时表");`

### `moveSheet(sheet, targetIndex)`

- **说明**: 移动一个工作表到新的索引位置。
- **参数**:
  - `sheet` (Sheet) - 要移动的 Sheet 对象。
  - `targetIndex` (number) - 目标位置的索引。
- **示例**: `Workbook.moveSheet(sheetToMove, 0);`

### `getRange(...)`

- **说明**: 在当前激活的工作表上获取一个范围 (Range)。此方法是 `getActiveSheet().getRange(...)` 的快捷方式。
- **重载**:
  - `getRange(a1Notation: string)` - 使用 A1 记号（如 "A1:B2"）。
  - `getRange(row, column, rowCount, columnCount)` - 使用行列索引和行列数。
- **返回**: `Range` - 获取到的区域对象。
- **示例**:

  ```javascript
  const range1 = Workbook.getRange("A1:Z1000");
  const range2 = Workbook.getRange(0, 0, 1000, 26); // 与上一行等效
  ```

### `getActiveCell()`

- **说明**: 获取当前激活的单元格。此方法是 `getActiveSheet().getActiveCell()` 的快捷方式。
- **返回**: `Range` - 代表单个单元格的 Range 对象。

### `getActiveRange()`

- **说明**: 获取激活的选区。如果激活了多个不连续区域，则返回activeCell所在的选区。
- **返回**: `Range | null` - 当前激活的选区。如果当前选中了图表等，则返回null。

### `getRangeList(a1Notations)`

- **说明**: 获取多个不连续的区域。此方法是 `getActiveSheet().getRangeList(...)` 的快捷方式。
- **参数**: `a1Notations` (string[]) - A1 记号字符串数组。
- **返回**: `RangeList` - 代表多个区域的集合。

### `newFilterCriteriaBuilder()`

- **说明**: 创建一个用于构建筛选条件的对象。
- **返回**: `FilterCriteriaBuilder` - 筛选条件构建器。

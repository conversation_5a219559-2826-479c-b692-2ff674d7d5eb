# 每日人力工作表功能PRD文档

## 1. 功能概述

### 1.1 功能名称

每日人力工作表(Auto)

### 1.2 功能描述

基于测试人力计划数据，自动生成按行业维度统计的每日人力需求工作表，帮助管理者了解各行业在不同日期的人力投入情况。

### 1.3 业务价值

- **资源规划**：帮助管理者合理分配人力资源
- **成本控制**：清晰了解各行业的人力成本分布
- **进度跟踪**：监控各行业项目的人力投入进度
- **决策支持**：为人力资源调配提供数据支持

## 2. 功能需求

### 2.1 核心功能

- 从源数据中提取行业信息和每日工时数据
- 按行业维度汇总每日人力需求
- 生成美观的统计表格
- 自动计算各行业总计工时

### 2.2 数据来源

- **源工作表**：测试人力计划工作表
- **行业字段**：第1列（支持合并单元格）
- **日期字段**：表头第2行的日期列
- **工时字段**：各日期列对应的工时数值

### 2.3 输出格式

#### 表格结构

| 行业 | 6月4日 | 6月5日 | ... | 10月31日 | 合计 |
|------|--------|--------|-----|----------|------|
| 前装 | 2.5    | 3.0    | ... | 1.0      | 45.5 |
| 国内货运 | 1.0 | 2.0 | ... | 0.5 | 28.5 |
| 渣土 | 0.5    | 1.5    | ... | 0.0      | 15.2 |

#### 字段说明

- **行业列**：显示各个行业名称
- **日期列**：显示各个工作日的工时（小时）
- **合计列**：显示各行业的总人力需求

## 3. 技术规格

### 3.1 数据处理逻辑

#### 行业信息提取

- 从第1列提取行业信息
- 支持合并单元格处理
- 对于空值使用默认值"未知行业"

#### 工时数据聚合

- 按行业和日期维度聚合工时数据
- 累加同一行业同一日期的所有工时
- 保持数据精度，避免浮点数误差

#### 合计计算

- 计算各行业的总工时
- 对所有日期的工时进行求和
- 确保计算结果的准确性

### 3.2 表格生成规则

#### 表头生成

- 第1列：行业标题
- 第2-N列：日期标题（格式：X月X日）
- 最后1列：合计标题

#### 数据填充

- 行业名称按字母顺序排序
- 工时数据保留1位小数
- 空值显示为空白

#### 样式设置

- 标题行：粗体字体，浅蓝色背景
- 数据行：交替行色（浅灰色背景）
- 列宽：行业列120px，日期列80px，合计列80px
- 行高：25px

### 3.3 配置参数

#### 工作表配置

- 工作表名称：每日人力(Auto)
- 如果工作表已存在，则清空后重新生成

#### 样式配置

- 标题行背景色：浅蓝色
- 交替行背景色：浅灰色
- 行高：25px
- 列宽：行业列120px，日期列80px，工时列80px
- 数字格式：保留1位小数

## 4. 用户交互

### 4.1 触发方式

- 脚本自动执行，无需用户手动触发
- 在个人任务日历生成完成后自动生成

### 4.2 输出位置

- 创建新工作表："每日人力(Auto)"
- 如果工作表已存在，则清空后重新生成

### 4.3 错误处理

- 数据为空时显示相应提示
- 工作表创建失败时记录错误日志
- 样式设置失败时继续执行，不中断流程

## 5. 数据示例

### 5.1 输入数据示例

基于测试人力计划表的数据结构：

- 日期：6/4, 6/5, 6/6...
- 任务描述：9924_AEBS项目集解决方案 - 第四轮
- 测试人员：张绍萌, 陈超
- 工时：1, 0.5, 2等数值
- 行业：前装, 国内货运, 渣土等

### 5.2 输出数据示例

| 行业 | 6月4日 | 6月5日 | 合计 |
|------|--------|--------|------|
| 前装 | 2.0    | 1.5    | 3.5  |
| 国内货运 | 0.5 | 1.0    | 1.5  |
| 渣土 | 1.0    | 0.0    | 1.0  |

## 6. 验收标准

### 6.1 功能验收

- [ ] 能正确提取行业信息（包括合并单元格）
- [ ] 能正确聚合各行业的每日工时数据
- [ ] 能生成格式正确的每日人力工作表
- [ ] 合计计算准确无误
- [ ] 表格样式美观，符合设计要求

### 6.2 数据验收

- [ ] 行业数据完整，无遗漏
- [ ] 工时数据准确，与源数据一致
- [ ] 日期范围正确，覆盖所有工作日
- [ ] 数值格式正确（保留1位小数）

### 6.3 性能验收

- [ ] 生成速度合理（<5秒）
- [ ] 内存使用正常，无泄漏
- [ ] 支持大量数据处理（>1000行）

### 6.4 兼容性验收

- [ ] 支持不同的行业名称格式
- [ ] 支持各种日期格式
- [ ] 兼容钉钉文档API限制

## 7. 风险评估

### 7.1 数据风险

- **风险**：行业信息缺失或格式不规范
- **影响**：中等
- **缓解措施**：使用默认值"未知行业"，支持多种格式

### 7.2 性能风险

- **风险**：大量数据处理导致性能问题
- **影响**：低
- **缓解措施**：优化数据结构，使用Map进行高效聚合

### 7.3 API风险

- **风险**：钉钉API限制或变更
- **影响**：中等
- **缓解措施**：充分的错误处理，降级策略

## 8. 后续优化

### 8.1 功能增强

- 支持自定义时间范围筛选
- 增加图表可视化功能
- 支持导出到其他格式

### 8.2 用户体验

- 增加数据验证提示
- 提供更详细的统计信息
- 支持交互式筛选和排序

### 8.3 性能优化

- 实现增量更新机制
- 优化大数据量处理
- 添加缓存机制

## 9. 测试用例

### 9.1 基础功能测试

1. **正常数据测试**：使用标准格式数据生成工作表
2. **空数据测试**：处理无工时数据的情况
3. **单行业测试**：只有一个行业的数据
4. **多行业测试**：包含多个行业的复杂数据

### 9.2 边界条件测试

1. **最大数据量测试**：处理大量行业和日期数据
2. **特殊字符测试**：行业名称包含特殊字符
3. **日期边界测试**：跨月份、跨年度的日期处理

### 9.3 异常情况测试

1. **工作表创建失败**：权限不足或API限制
2. **样式设置失败**：API不支持或参数错误
3. **数据格式异常**：非预期的数据格式

## 10. 更新日志

待后续更新时记录

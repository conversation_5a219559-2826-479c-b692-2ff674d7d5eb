# 钉钉文档脚本 - 测试人力计划解析器（重构版）

## 概述

这是一个完全重构的钉钉文档脚本，用于解析测试人力计划Excel表格，提取并分析每个人每天的工作内容和工时数据。重构版本采用配置驱动的架构，支持多种数据源类型，更易于维护和扩展。

## 主要改进

### 1. 配置驱动架构
- **统一配置对象**：所有配置集中在 `CONFIG` 对象中
- **输入配置**：支持多种数据源类型（测试任务日历、公共事项日历）
- **输出配置**：可配置的输出格式和样式
- **全局配置**：日期范围、工时限制、日志级别等

### 2. 数据源分离
- **测试任务日历**：从行业列获取行业信息，优先使用任务名称
- **公共事项日历**：从事项内容提取行业信息，优先使用任务描述
- **独立处理函数**：每种数据源有专门的解析函数

### 3. 模块化设计
- **核心处理函数**：数据源处理、表头解析、任务信息提取
- **辅助函数**：日期转换、数据合并、空行判断等
- **输出生成函数**：个人工时日历、行业工时日历生成
- **样式应用函数**：表格样式、格式设置

## 配置结构

### 输入配置 (CONFIG.input)

```javascript
// 测试任务日历配置
testTaskCalendar: {
  sheetName: "测试任务日历",
  type: "testTask",
  dateRow: 1,
  dataStartRow: 2,
  columns: {
    industry: { index: 0, name: "行业" },
    projectIteration: { name: "项目_迭代（与RDMS一致）", keywords: ["项目_迭代"] },
    taskName: { name: "事项名称", keywords: ["事项名称"] },
    testPersonnel: { name: "测试人员", keywords: ["测试人员"] }
  },
  processing: {
    descriptionPreference: "taskName",
    personnelSeparators: /[,，、\s.。;；|]+/,
    industryFromColumn: true,
    supportMergedCells: true
  }
}

// 公共事项日历配置
publicEventCalendar: {
  sheetName: "公共事项日历",
  type: "publicEvent",
  dateRow: 0,
  dataStartRow: 1,
  // ... 类似配置
  processing: {
    descriptionPreference: "taskDescription",
    industryFromContent: true,
    defaultIndustry: "公共事项"
  }
}
```

### 输出配置 (CONFIG.output)

```javascript
// 个人工时日历配置
personalTaskCalendar: {
  sheetName: "个人工时日历(Auto)",
  enabled: true,
  structure: {
    columnsPerDate: 6,
    columns: [
      { name: "任务描述", width: 200 },
      { name: "单项工时(小时)", width: 120, format: "0.00" },
      { name: "折算工时(小时)", width: 120, format: "0.00" },
      { name: "行业", width: 120 },
      { name: "行业合计工时(小时)", width: 120, format: "0.00" },
      { name: "合计工时(小时)", width: 120, format: "0.00" }
    ]
  },
  styles: {
    headerBackgroundColor: '#0171C1',
    headerFontColor: '#FFFFFF',
    dateFormat: 'm"月"d"日"',
    freezePanes: { rows: 2, columns: 1 }
  }
}

// 行业工时日历配置
industryWorkCalendar: {
  sheetName: "行业工时日历(Auto)",
  enabled: true,
  structure: {
    columnsPerDate: 2,
    columns: [
      { name: "人力", width: 120, format: "0" },
      { name: "工时(小时)", width: 120, format: "0.00" }
    ]
  }
}
```

### 全局配置 (CONFIG.global)

```javascript
global: {
  dateRange: {
    minSerial: 40000,
    maxSerial: 80000
  },
  workHours: {
    maxDailyHours: 8,
    highlightOvertime: true,
    overtimeColor: '#FF0000'
  },
  logging: {
    enabled: true,
    level: "info"
  }
}
```

## 核心功能

### 1. 数据源处理
- `processDataSource()`: 统一的数据源处理入口
- `parseTestTaskSheet()`: 解析测试任务日历
- `parsePublicEventSheet()`: 解析公共事项日历

### 2. 数据解析
- `parseHeaders()`: 智能表头识别，支持关键词匹配
- `extractTaskInfo()`: 提取任务信息，支持合并单元格
- `addTaskToDaily()`: 构建每日工作映射

### 3. 数据合并
- `mergeAnalyses()`: 合并多个数据源的结果
- 按日期序列号排序，确保时间顺序正确

### 4. 输出生成
- `generatePersonalTaskCalendar()`: 生成个人工时日历
- `generateIndustryWorkCalendar()`: 生成行业工时日历
- 支持配置驱动的表格结构和样式

### 5. 样式应用
- `applyPersonalCalendarStyles()`: 个人日历样式
- `applyIndustryCalendarStyles()`: 行业日历样式
- 支持颜色、字体、列宽、行高等设置

## 使用方法

### 1. 配置修改
根据实际需求修改 `CONFIG` 对象中的配置：
- 修改工作表名称
- 调整列名关键词
- 设置输出样式
- 配置日期范围等

### 2. 运行脚本
在钉钉文档中运行脚本，系统会：
1. 自动识别配置的数据源工作表
2. 解析数据并合并结果
3. 生成统计信息
4. 创建个人工时日历和行业工时日历

### 3. 结果查看
- 查看控制台日志了解处理过程
- 检查生成的工作表内容
- 验证统计数据的准确性

## 扩展性

### 添加新数据源类型
1. 在 `CONFIG.input` 中添加新的数据源配置
2. 创建对应的解析函数（如 `parseNewTypeSheet()`）
3. 在 `processDataSource()` 中添加新类型的处理分支

### 自定义输出格式
1. 修改 `CONFIG.output` 中的结构配置
2. 调整表格生成函数中的列数和内容
3. 更新样式应用函数

### 增强数据处理
1. 扩展 `extractTaskInfo()` 函数支持更多字段
2. 添加数据验证和清洗逻辑
3. 实现更复杂的业务规则

## 错误处理

脚本包含完善的错误处理机制：
- 工作表不存在时自动跳过
- API调用失败时记录错误并继续执行
- 数据格式异常时提供详细的错误信息
- 样式设置失败时不影响核心功能

## 性能优化

- 使用Map数据结构提高查找效率
- 批量处理数据减少API调用次数
- 智能的表头解析避免重复计算
- 合理的内存管理防止内存泄漏

## 维护建议

1. **定期检查配置**：确保工作表名称和列名配置与实际数据一致
2. **监控日志输出**：关注错误和警告信息，及时处理异常
3. **测试新功能**：添加新功能时充分测试各种边界情况
4. **文档更新**：配置变更时及时更新相关文档

## 版本历史

### v2.0.0 (重构版)
- 完全重构代码架构
- 实现配置驱动设计
- 分离数据源处理逻辑
- 增强错误处理和日志记录
- 提高代码可维护性和扩展性

### v1.x.x (原版本)
- 基础功能实现
- 支持测试任务日历和公共事项日历
- 生成个人工时日历和行业工时日历

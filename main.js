/**
 * 钉钉文档脚本 - 测试人力计划解析器（重构版）
 * 解析多种类型的工作表，提取每个人每天的工作内容和工时
 * 支持配置驱动的数据源处理和输出格式定制
 */

// ==================== 主配置对象 ====================

const PERSONNEL_SEPARATORS = /[,，、\s.。;；|]+/;

const CONFIG = {
  // 输入数据源配置
  input: {
    // 测试任务日历数据源配置
    testTaskCalendar: {
      sheetName: "测试任务日历",
      type: "testTask", // 数据源类型标识
      dateRow: 1, // 日期所在行（从0开始）
      dataStartRow: 2, // 数据开始行（从0开始）
      columns: {
        industry: { index: 0, name: "行业" }, // 行业列（第A列）
        projectIteration: { name: "项目_迭代（与RDMS一致）", keywords: ["项目_迭代"] },
        taskName: { name: "事项名称", keywords: ["事项名称"] },
        taskDescription: { name: "任务描述", keywords: ["任务描述"] },
        testPersonnel: { name: "测试人员", keywords: ["测试人员"] }
      },
      // 数据处理配置
      processing: {
        descriptionPreference: "taskName", // 优先使用任务名称
      }
    },

    // 公共事项日历数据源配置
    publicEventCalendar: {
      sheetName: "公共事项日历",
      type: "publicEvent", // 数据源类型标识
      dateRow: 0, // 日期所在行（从0开始）
      dataStartRow: 1, // 数据开始行（从0开始）
      columns: {
        // 公共事项日历没有独立的行业列，从事项内容中提取
        projectIteration: { name: "项目_迭代", keywords: ["项目_迭代"] },
        taskName: { name: "事项名称", keywords: ["事项名称"] },
        taskDescription: { name: "任务描述", keywords: ["任务描述"] },
        testPersonnel: { name: "测试人员", keywords: ["测试人员"] }
      },
      // 数据处理配置
      processing: {
        descriptionPreference: "taskDescription", // 优先使用任务描述
        industryFromContent: true, // 从事项内容中提取行业
        defaultIndustry: "公共事项", // 默认行业名称
      }
    }
  },

  // 输出配置
  output: {
    // 个人工时日历输出配置
    personalTaskCalendar: {
      sheetName: "个人工时日历(Auto)",
      enabled: true,
      structure: {
        dateHeaderRow: 0, // 日期标题行
        columnHeaderRow: 1, // 列标题行
        dataStartRow: 2, // 数据开始行
        columnsPerDate: 6, // 每个日期占用的列数
        columns: [
          { name: "任务描述", width: 200 },
          { name: "单项工时(小时)", width: 120, format: "0.00" },
          { name: "折算工时(小时)", width: 120, format: "0.00" },
          { name: "行业", width: 120 },
          { name: "行业合计工时(小时)", width: 120, format: "0.00" },
          { name: "合计工时(小时)", width: 120, format: "0.00" }
        ]
      },
      styles: {
        headerBackgroundColor: '#0171C1',
        headerFontColor: '#FFFFFF',
        headerFontWeight: 'bold',
        alternateRowBackgroundColor: '#FAFAFA',
        leftTitleColumnBackgroundColor: '#92D04F',
        nameColumnWidth: 80,
        rowHeight: 25,
        dateFormat: 'm"月"d"日"',
        freezePanes: { rows: 2, columns: 1 }
      }
    },

    // 行业工时日历输出配置
    industryWorkCalendar: {
      sheetName: "行业工时日历(Auto)",
      enabled: true,
      structure: {
        dateHeaderRow: 0, // 日期标题行
        columnHeaderRow: 1, // 列标题行
        dataStartRow: 2, // 数据开始行
        columnsPerDate: 2, // 每个日期占用的列数（人力、工时）
        columns: [
          { name: "人力", width: 120, format: "0" },
          { name: "工时(小时)", width: 120, format: "0.00" }
        ]
      },
      styles: {
        headerBackgroundColor: '#0171C1',
        headerFontColor: '#FFFFFF',
        headerFontWeight: 'bold',
        alternateRowBackgroundColor: '#FAFAFA',
        leftTitleColumnBackgroundColor: '#92D04F',
        industryColumnWidth: 120,
        rowHeight: 25,
        dateFormat: 'm"/"d',
        freezePanes: { rows: 2, columns: 1 }
      }
    }
  },

  // 全局配置
  global: {
    dateRange: {
      minSerial: 40000, // Excel日期序列号最小值
      maxSerial: 80000  // Excel日期序列号最大值
    },
    workHours: {
      maxDailyHours: 8, // 每日最大工时，超过则按比例折算
      highlightOvertime: true, // 是否高亮显示超时
      overtimeColor: '#FF0000' // 超时显示颜色
    },
    logging: {
      enabled: true,
      level: "info" // debug, info, warn, error
    }
  }
};

// ==================== 主函数 ====================

async function main() {
  console.log("[main] 🚀 开始执行脚本（重构版）");

  try {
    // 获取所有工作表
    const sheets = Workbook.getSheets();
    console.log("[main] 获取到工作表:", sheets.map(sheet => sheet.getName()));

    if (sheets.length === 0) {
      Output.error("[main] 错误：工作簿中没有找到任何工作表");
      return;
    }

    // 使用新的配置驱动逻辑解析数据源
    const allAnalyses = [];

    // 处理测试任务日历
    const testTaskAnalysis = await processDataSource(CONFIG.input.testTaskCalendar);
    if (testTaskAnalysis) {
      allAnalyses.push(testTaskAnalysis);
    }

    // 处理公共事项日历
    const publicEventAnalysis = await processDataSource(CONFIG.input.publicEventCalendar);
    if (publicEventAnalysis) {
      allAnalyses.push(publicEventAnalysis);
    }

    console.log("[main] 数据源解析完成，共处理", allAnalyses.length, "个数据源");

    // 合并所有数据源的结果
    const mergedAnalysis = mergeAnalyses(allAnalyses);
    console.log("[main] 数据合并完成，共", mergedAnalysis.dailyWorkAnalysis.length, "个工作日");

    // 输出统计信息
    if (mergedAnalysis.dailyWorkAnalysis.length > 0) {
      outputStatistics(mergedAnalysis);

      // 生成个人工时日历
      if (CONFIG.output.personalTaskCalendar.enabled) {
        generatePersonalTaskCalendar(mergedAnalysis.dailyWorkAnalysis);
        console.log("[main] 个人工时日历生成完成");
      }

      // 生成行业工时日历
      if (CONFIG.output.industryWorkCalendar.enabled) {
        generateIndustryWorkCalendar(mergedAnalysis.dailyWorkAnalysis);
        console.log("[main] 行业工时日历生成完成");
      }

      Output.log("[main] === 所有任务完成 ===");
    } else {
      Output.log("[main] 未找到有效数据，跳过输出生成");
    }

  } catch (error) {
    Output.error("[main] 脚本执行出错: " + error.message);
    if (error.stack) {
      Output.error("[main] 错误堆栈: " + error.stack);
    }
  }

  console.log("[main] 🚀 脚本执行结束");
}

// ==================== 核心数据处理函数 ====================

/**
 * 处理单个数据源
 * @param {Object} sourceConfig - 数据源配置
 * @returns {Object|null} 解析后的数据分析结果
 */
async function processDataSource(sourceConfig) {
  try {
    // 尝试获取工作表
    let sheet;
    try {
      sheet = Workbook.getSheet(sourceConfig.sheetName);
    } catch (_error) {
      console.log(`[processDataSource] 工作表 "${sourceConfig.sheetName}" 不存在，跳过处理`);
      return null;
    }

    console.log(`[processDataSource] 开始处理数据源: ${sourceConfig.sheetName}`);

    // 根据数据源类型选择处理函数
    let analysis;
    switch (sourceConfig.type) {
      case 'testTask':
        analysis = await parseTestTaskSheet(sheet, sourceConfig);
        break;
      case 'publicEvent':
        analysis = await parsePublicEventSheet(sheet, sourceConfig);
        break;
      default:
        console.log(`[processDataSource] 未知的数据源类型: ${sourceConfig.type}`);
        return null;
    }

    console.log(`[processDataSource] 数据源 "${sourceConfig.sheetName}" 处理完成，共 ${analysis.dailyWorkAnalysis.length} 个工作日`);
    return analysis;

  } catch (error) {
    Output.error(`[processDataSource] 处理数据源 "${sourceConfig.sheetName}" 时出错: ${error.message}`);
    return null;
  }
}

/**
 * 解析测试任务日历工作表
 * @param {Sheet} sheet - 工作表对象
 * @param {Object} config - 配置对象
 * @returns {Object} 解析后的工作分析数据
 */
async function parseTestTaskSheet(sheet, config) {
  console.log(`[parseTestTaskSheet] 开始解析测试任务日历`);

  // 获取工作表数据
  const sheetData = getSheetData(sheet);
  if (!sheetData) {
    return { dailyWorkAnalysis: [] };
  }

  // 解析表头，获取列映射
  const columnMap = parseHeaders(sheetData.values, config);
  if (!columnMap.dateColumns.length) {
    Output.error(`[parseTestTaskSheet] 未找到有效的日期列`);
    return { dailyWorkAnalysis: [] };
  }

  // 解析数据行
  const dailyWorkMap = new Map();
  let validRows = 0;
  let lastProjectIteration = "";
  let lastIndustry = "";

  for (let i = config.dataStartRow; i < sheetData.values.length; i++) {
    const row = sheetData.values[i];

    // 跳过空行
    if (isEmptyRow(row)) continue;

    // 提取任务信息
    const taskInfo = extractTaskInfo(row, columnMap, config, lastProjectIteration);
    if (!taskInfo.taskDescription) continue;

    // 提取行业信息（从第一列）
    const currentIndustry = String(row[config.columns.industry.index] || "").trim();
    const industry = currentIndustry || lastIndustry || "未知行业";

    if (currentIndustry) {
      lastIndustry = currentIndustry;
    }

    if (taskInfo.currentProjectIteration) {
      lastProjectIteration = taskInfo.currentProjectIteration;
    }

    validRows++;

    // 处理每个日期列的工时数据
    columnMap.dateColumns.forEach(dateCol => {
      const workHours = parseFloat(row[dateCol.index]) || 0;
      if (workHours > 0) {
        addTaskToDaily(dailyWorkMap, dateCol.serial, {
          industry,
          taskDescription: taskInfo.taskDescription,
          testPersonnel: taskInfo.testPersonnel,
          workHours
        });
      }
    });
  }

  console.log(`[parseTestTaskSheet] 解析完成，有效行数: ${validRows}，工作日数: ${dailyWorkMap.size}`);

  return {
    dailyWorkAnalysis: Array.from(dailyWorkMap.values()).sort((a, b) => a.dateSerial - b.dateSerial)
  };
}

/**
 * 解析公共事项日历工作表
 * @param {Sheet} sheet - 工作表对象
 * @param {Object} config - 配置对象
 * @returns {Object} 解析后的工作分析数据
 */
async function parsePublicEventSheet(sheet, config) {
  console.log(`[parsePublicEventSheet] 开始解析公共事项日历`);

  // 获取工作表数据
  const sheetData = getSheetData(sheet);
  if (!sheetData) {
    return { dailyWorkAnalysis: [] };
  }

  // 解析表头，获取列映射
  const columnMap = parseHeaders(sheetData.values, config);
  if (!columnMap.dateColumns.length) {
    Output.error(`[parsePublicEventSheet] 未找到有效的日期列`);
    return { dailyWorkAnalysis: [] };
  }

  // 解析数据行
  const dailyWorkMap = new Map();
  let validRows = 0;
  let lastProjectIteration = "";

  for (let i = config.dataStartRow; i < sheetData.values.length; i++) {
    const row = sheetData.values[i];

    // 跳过空行
    if (isEmptyRow(row)) continue;

    // 提取任务信息
    const taskInfo = extractTaskInfo(row, columnMap, config, lastProjectIteration);
    if (!taskInfo.taskDescription) continue;

    // 从事项内容中提取行业信息
    let industry = config.processing.defaultIndustry;
    if (config.processing.industryFromContent && taskInfo.taskDescription) {
      const parts = taskInfo.taskDescription.split(' - ');
      if (parts.length > 1) {
        industry = parts[1].trim();
      }
    }

    if (taskInfo.currentProjectIteration) {
      lastProjectIteration = taskInfo.currentProjectIteration;
    }

    validRows++;

    // 处理每个日期列的工时数据
    columnMap.dateColumns.forEach(dateCol => {
      const workHours = parseFloat(row[dateCol.index]) || 0;
      if (workHours > 0) {
        addTaskToDaily(dailyWorkMap, dateCol.serial, {
          industry,
          taskDescription: taskInfo.taskDescription,
          testPersonnel: taskInfo.testPersonnel,
          workHours
        });
      }
    });
  }

  console.log(`[parsePublicEventSheet] 解析完成，有效行数: ${validRows}，工作日数: ${dailyWorkMap.size}`);

  return {
    dailyWorkAnalysis: Array.from(dailyWorkMap.values()).sort((a, b) => a.dateSerial - b.dateSerial)
  };
}

// ==================== 辅助函数 ====================

/**
 * 获取工作表数据
 * @param {Sheet} sheet - 工作表对象
 * @returns {Object|null} 包含values数组的对象，或null
 */
function getSheetData(sheet) {
  try {
    // 尝试获取较大范围的数据
    const dataRange = sheet.getRange("A1:AZ1000");
    const values = dataRange.getValues();

    // 移除完全空的行
    while (values.length > 0 && values[values.length - 1].every(cell => !cell || String(cell).trim() === "")) {
      values.pop();
    }

    if (values.length === 0) {
      Output.error("[getSheetData] 工作表为空");
      return null;
    }

    return { values };
  } catch (error) {
    Output.error(`[getSheetData] 获取工作表数据失败: ${error.message}`);
    return null;
  }
}

/**
 * 解析表头，识别各列位置
 * @param {Array} values - 工作表数据
 * @param {Object} config - 配置对象
 * @returns {Object} 列映射对象
 */
function parseHeaders(values, config) {
  const columnMap = {
    projectIteration: -1,
    taskName: -1,
    taskDescription: -1,
    testPersonnel: -1,
    dateColumns: []
  };

  if (values.length <= config.dateRow) {
    Output.error("[parseHeaders] 数据行数不足，无法解析表头");
    return columnMap;
  }

  const headerRow1 = values[0];
  const dateHeaderRow = values[config.dateRow];
  const numCols = Math.max(headerRow1.length, dateHeaderRow.length);

  // 解析常规列
  for (let index = 0; index < numCols; index++) {
    const header1Str = String(headerRow1[index] || "").trim().toLowerCase();

    // 匹配项目迭代列
    if (config.columns.projectIteration && config.columns.projectIteration.keywords) {
      if (config.columns.projectIteration.keywords.some(kw => header1Str.includes(kw.toLowerCase()))) {
        columnMap.projectIteration = index;
      }
    }

    // 匹配任务名称列
    if (config.columns.taskName && config.columns.taskName.keywords) {
      if (config.columns.taskName.keywords.some(kw => header1Str.includes(kw.toLowerCase()))) {
        columnMap.taskName = index;
      }
    }

    // 匹配任务描述列
    if (config.columns.taskDescription && config.columns.taskDescription.keywords) {
      if (config.columns.taskDescription.keywords.some(kw => header1Str.includes(kw.toLowerCase()))) {
        columnMap.taskDescription = index;
      }
    }

    // 匹配测试人员列
    if (config.columns.testPersonnel && config.columns.testPersonnel.keywords) {
      if (config.columns.testPersonnel.keywords.some(kw => header1Str.includes(kw.toLowerCase()))) {
        columnMap.testPersonnel = index;
      }
    }
  }

  // 解析日期列
  for (let index = 0; index < dateHeaderRow.length; index++) {
    const dateHeaderValue = dateHeaderRow[index];
    if (typeof dateHeaderValue === 'number' && Number.isFinite(dateHeaderValue)) {
      if (dateHeaderValue > CONFIG.global.dateRange.minSerial && dateHeaderValue < CONFIG.global.dateRange.maxSerial) {
        columnMap.dateColumns.push({ index, serial: Math.round(dateHeaderValue) });
      }
    }
  }

  console.log(`[parseHeaders] 找到 ${columnMap.dateColumns.length} 个日期列`);
  return columnMap;
}

/**
 * 提取任务基本信息
 * @param {Array} row - 数据行
 * @param {Object} columnMap - 列映射
 * @param {Object} config - 配置对象
 * @param {string} lastProjectIteration - 上一行的项目迭代信息
 * @returns {Object} 任务信息
 */
function extractTaskInfo(row, columnMap, config, lastProjectIteration = "") {
  // 提取当前行的项目迭代信息
  const currentProjectIteration = columnMap.projectIteration >= 0 ?
    String(row[columnMap.projectIteration] || "").trim() : "";

  // 决定使用哪个项目迭代信息
  const projectIteration = currentProjectIteration || lastProjectIteration;

  // 提取基础描述字段
  const taskName = columnMap.taskName >= 0 ? String(row[columnMap.taskName] || "").trim() : "";
  const taskDescField = columnMap.taskDescription >= 0 ? String(row[columnMap.taskDescription] || "").trim() : "";

  const baseDescription = (config.processing.descriptionPreference === 'taskDescription')
    ? (taskDescField || taskName)
    : (taskName || taskDescField);

  // 拼接任务描述
  let taskDescription = "";
  if (projectIteration && baseDescription) {
    taskDescription = `${projectIteration} - ${baseDescription}`;
  } else if (projectIteration) {
    taskDescription = projectIteration;
  } else if (baseDescription) {
    taskDescription = baseDescription;
  }

  // 提取测试人员
  const testPersonnelStr = columnMap.testPersonnel >= 0 ?
    String(row[columnMap.testPersonnel] || "").trim() : "";

  const testPersonnel = testPersonnelStr ?
    testPersonnelStr.split(PERSONNEL_SEPARATORS).filter(name => name.length > 0) : [];

  return {
    taskDescription,
    testPersonnel,
    currentProjectIteration
  };
}

/**
 * 添加任务到每日工作映射
 * @param {Map} dailyWorkMap - 每日工作映射
 * @param {number} dateSerial - 日期序列号
 * @param {Object} task - 任务对象
 */
function addTaskToDaily(dailyWorkMap, dateSerial, task) {
  if (!dailyWorkMap.has(dateSerial)) {
    dailyWorkMap.set(dateSerial, { dateSerial, tasks: [] });
  }
  dailyWorkMap.get(dateSerial).tasks.push(task);
}

/**
 * 合并多个分析结果
 * @param {Array} analyses - 分析结果数组
 * @returns {Object} 合并后的结果
 */
function mergeAnalyses(analyses) {
  const mergedMap = new Map();

  analyses.filter(Boolean).forEach(analysis => {
    if (analysis && analysis.dailyWorkAnalysis) {
      analysis.dailyWorkAnalysis.forEach(day => {
        const dateKey = day.dateSerial;
        if (!mergedMap.has(dateKey)) {
          mergedMap.set(dateKey, { dateSerial: dateKey, tasks: [] });
        }
        const entry = mergedMap.get(dateKey);
        if (Array.isArray(day.tasks) && day.tasks.length > 0) {
          entry.tasks.push(...day.tasks);
        }
      });
    }
  });

  const dailyWorkAnalysis = Array.from(mergedMap.values())
    .sort((a, b) => a.dateSerial - b.dateSerial);

  return { dailyWorkAnalysis };
}

/**
 * 判断是否为空行
 * @param {Array} row - 数据行
 * @returns {boolean} 是否为空行
 */
function isEmptyRow(row) {
  return row.every(cell => {
    const cellValue = String(cell || "").trim();
    return cellValue === "" || cellValue === "0";
  });
}

/**
 * 将 Excel 日期序列号转换为 JS Date 对象
 * @param {number} serial - Excel 日期序列号
 * @returns {Date | null} JS Date 对象或 null
 */
function excelSerialToJsDate(serial) {
  if (typeof serial !== 'number' || !Number.isFinite(serial)) return null;
  const dayMs = 24 * 60 * 60 * 1000;
  const excelEpochUTC = Date.UTC(1899, 11, 30); // 1899-12-30
  const millis = excelEpochUTC + Math.round(serial) * dayMs;
  return new Date(millis);
}

/**
 * 格式化日期序列号用于显示
 * @param {number} serial - Excel 日期序列号
 * @returns {string} 格式化后的日期字符串
 */
function formatDateSerialForDisplay(serial) {
  const date = excelSerialToJsDate(serial);
  if (!date) return "";
  const month = date.getUTCMonth() + 1;
  const day = date.getUTCDate();
  return `${month}/${day}`;
}

// ==================== 统计和输出函数 ====================

/**
 * 输出统计信息
 * @param {Object} workAnalysis - 工作分析数据
 */
function outputStatistics(workAnalysis) {
  Output.log("[outputStatistics] === 统计信息 ===");

  const { dailyWorkAnalysis } = workAnalysis;

  if (dailyWorkAnalysis.length === 0) {
    Output.error("[outputStatistics] 无统计数据");
    return;
  }

  // 总体统计
  const totalDays = dailyWorkAnalysis.length;
  const totalTasks = dailyWorkAnalysis.reduce((sum, day) => sum + day.tasks.length, 0);
  const totalWorkHours = dailyWorkAnalysis.reduce((sum, day) =>
    sum + day.tasks.reduce((daySum, task) => daySum + task.workHours, 0), 0);

  Output.log(`[outputStatistics] 总天数: ${totalDays}`);
  Output.log(`[outputStatistics] 总任务数: ${totalTasks}`);
  Output.log(`[outputStatistics] 总工时: ${totalWorkHours.toFixed(1)} 小时`);

  // 人员工时统计
  const personnelStats = new Map();
  dailyWorkAnalysis.forEach(day => {
    day.tasks.forEach(task => {
      task.testPersonnel.forEach(person => {
        if (!personnelStats.has(person)) {
          personnelStats.set(person, 0);
        }
        personnelStats.set(person, personnelStats.get(person) + task.workHours);
      });
    });
  });

  if (personnelStats.size > 0) {
    Output.log("[outputStatistics] 人员工时统计:");
    Array.from(personnelStats.entries())
      .sort((a, b) => b[1] - a[1])
      .forEach(([person, hours]) => {
        Output.log(`[outputStatistics] 人员工时, person: ${person}, hours: ${hours.toFixed(1)}`);
      });
  }

  // 每日工时统计
  Output.log("[outputStatistics] 每日工时统计:");
  dailyWorkAnalysis.forEach(day => {
    const dayTotal = day.tasks.reduce((sum, task) => sum + task.workHours, 0);
    Output.log(`[outputStatistics] 每日工时, date: ${formatDateSerialForDisplay(day.dateSerial)}, hours: ${dayTotal.toFixed(1)}, tasks: ${day.tasks.length}`);
  });
}

/**
 * 生成个人工时日历
 * @param {Array} dailyWorkAnalysis - 每日工作分析数据
 */
function generatePersonalTaskCalendar(dailyWorkAnalysis) {
  try {
    const config = CONFIG.output.personalTaskCalendar;

    // 检查并删除已存在的工作表
    let sheet;
    try {
      const existingSheet = Workbook.getSheet(config.sheetName);
      if (existingSheet) {
        Workbook.deleteSheet(existingSheet);
      }
    } catch (_error) {
      // 工作表不存在，忽略错误
    }

    // 创建新工作表
    sheet = Workbook.insertSheet(config.sheetName);
    if (!sheet) {
      Output.error(`[generatePersonalTaskCalendar] 无法创建工作表: ${config.sheetName}`);
      return;
    }

    // 获取所有日期并排序
    const allDates = Array.from(new Set(dailyWorkAnalysis.map(day => day.dateSerial)))
      .sort((a, b) => a - b);

    // 构建人员任务数据结构
    const personnelTaskData = buildPersonnelTaskData(dailyWorkAnalysis);

    // 生成表格
    generatePersonalCalendarTable(sheet, personnelTaskData, allDates, config);

    // 应用样式
    applyPersonalCalendarStyles(sheet, allDates, config);

    // 冻结窗格
    try {
      sheet.setFrozenRowCount(config.styles.freezePanes.rows);
      sheet.setFrozenColumnCount(config.styles.freezePanes.columns);
    } catch (_error) {
      // 冻结窗格失败，忽略
    }

    Output.log("[generatePersonalTaskCalendar] 个人工时日历生成完成");

  } catch (error) {
    Output.error("[generatePersonalTaskCalendar] 生成个人工时日历时出错: " + error.message);
  }
}

/**
 * 生成行业工时日历
 * @param {Array} dailyWorkAnalysis - 每日工作分析数据
 */
function generateIndustryWorkCalendar(dailyWorkAnalysis) {
  try {
    const config = CONFIG.output.industryWorkCalendar;

    // 检查并删除已存在的工作表
    let sheet;
    try {
      const existingSheet = Workbook.getSheet(config.sheetName);
      if (existingSheet) {
        Workbook.deleteSheet(existingSheet);
      }
    } catch (_error) {
      // 工作表不存在，忽略错误
    }

    // 创建新工作表
    sheet = Workbook.insertSheet(config.sheetName);
    if (!sheet) {
      Output.error(`[generateIndustryWorkCalendar] 无法创建工作表: ${config.sheetName}`);
      return;
    }

    // 计算行业统计数据
    const industryStats = calculateIndustryStats(dailyWorkAnalysis);

    // 获取所有日期并排序
    const allDatesSet = new Set();
    industryStats.forEach(dateMap => {
      dateMap.forEach((_val, dateKey) => allDatesSet.add(Number(dateKey)));
    });
    const sortedDates = Array.from(allDatesSet).sort((a, b) => a - b);

    // 生成表格
    generateIndustryCalendarTable(sheet, industryStats, sortedDates, config);

    // 应用样式
    applyIndustryCalendarStyles(sheet, sortedDates, config);

    // 冻结窗格
    try {
      sheet.setFrozenRowCount(config.styles.freezePanes.rows);
      sheet.setFrozenColumnCount(config.styles.freezePanes.columns);
    } catch (_error) {
      // 冻结窗格失败，忽略
    }

    Output.log("[generateIndustryWorkCalendar] 行业工时日历生成完成");

  } catch (error) {
    Output.error("[generateIndustryWorkCalendar] 生成行业工时日历时出错: " + error.message);
  }
}

// ==================== 表格生成辅助函数 ====================

/**
 * 构建人员任务数据结构
 * @param {Array} dailyWorkAnalysis - 每日工作分析数据
 * @returns {Map} 人员任务数据映射
 */
function buildPersonnelTaskData(dailyWorkAnalysis) {
  const personnelTaskData = new Map(); // Map<Person, Map<dateSerial, Task[]>>

  dailyWorkAnalysis.forEach(day => {
    const dateKey = day.dateSerial;
    day.tasks.forEach(task => {
      task.testPersonnel.forEach(person => {
        if (!personnelTaskData.has(person)) {
          personnelTaskData.set(person, new Map());
        }
        const personMap = personnelTaskData.get(person);
        if (!personMap.has(dateKey)) {
          personMap.set(dateKey, []);
        }

        personMap.get(dateKey).push({
          taskDescription: task.taskDescription,
          workHours: task.workHours,
          industry: task.industry
        });
      });
    });
  });

  return personnelTaskData;
}

/**
 * 计算行业统计数据
 * @param {Array} dailyWorkAnalysis - 每日工作分析数据
 * @returns {Map} 行业统计数据映射
 */
function calculateIndustryStats(dailyWorkAnalysis) {
  const tempMap = new Map(); // Map<Industry, Map<DateKey, { people: Set<string>, hours: number }>>

  dailyWorkAnalysis.forEach(day => {
    const dateKey = day.dateSerial;
    day.tasks.forEach(task => {
      if (!task.industry) return;

      if (!tempMap.has(task.industry)) {
        tempMap.set(task.industry, new Map());
      }
      const industryDateMap = tempMap.get(task.industry);

      if (!industryDateMap.has(dateKey)) {
        industryDateMap.set(dateKey, { people: new Set(), hours: 0 });
      }
      const entry = industryDateMap.get(dateKey);

      // 工时累计
      entry.hours += (parseFloat(task.workHours) || 0);

      // 人员集合
      if (Array.isArray(task.testPersonnel)) {
        task.testPersonnel.forEach(person => {
          const name = String(person || '').trim();
          if (name) entry.people.add(name);
        });
      }
    });
  });

  // 转换为最终格式
  const result = new Map(); // Map<Industry, Map<DateKey, { headcount, hours }>>
  tempMap.forEach((dateMap, industry) => {
    const outDateMap = new Map();
    dateMap.forEach((entry, dateKey) => {
      outDateMap.set(dateKey, { headcount: entry.people.size, hours: entry.hours });
    });
    result.set(industry, outDateMap);
  });

  return result;
}

/**
 * 生成个人日历表格
 * @param {Sheet} sheet - 工作表对象
 * @param {Map} personnelTaskData - 人员任务数据
 * @param {Array} allDates - 所有日期
 * @param {Object} config - 配置对象
 */
function generatePersonalCalendarTable(sheet, personnelTaskData, allDates, config) {
  let currentRow = 0;

  // 生成日期标题行
  allDates.forEach((serial, index) => {
    const colOffset = index * config.structure.columnsPerDate + 1;
    const dateCell = sheet.getCell(currentRow, colOffset);
    dateCell.setValue(serial);
    dateCell.setNumberFormat(config.styles.dateFormat);

    // 合并日期标题单元格
    try {
      const dateHeaderRange = sheet.getRange(currentRow, colOffset, 1, config.structure.columnsPerDate);
      dateHeaderRange.merge();
    } catch (_error) {
      // 合并失败，忽略
    }
  });
  currentRow++;

  // 生成列标题行
  sheet.getCell(currentRow, 0).setValue("姓名");
  allDates.forEach((_, index) => {
    const colOffset = index * config.structure.columnsPerDate + 1;
    config.structure.columns.forEach((col, colIndex) => {
      sheet.getCell(currentRow, colOffset + colIndex).setValue(col.name);
    });
  });
  currentRow++;

  // 生成人员数据
  const sortedPersonnel = Array.from(personnelTaskData.keys()).sort();
  sortedPersonnel.forEach(person => {
    const personData = personnelTaskData.get(person);
    let maxRowsForPerson = 1;

    // 计算这个人需要的最大行数
    allDates.forEach(serial => {
      const tasks = personData.get(serial) || [];
      maxRowsForPerson = Math.max(maxRowsForPerson, tasks.length);
    });

    // 设置人员姓名
    sheet.getCell(currentRow, 0).setValue(person);
    if (maxRowsForPerson > 1) {
      try {
        const nameRange = sheet.getRange(currentRow, 0, maxRowsForPerson, 1);
        nameRange.merge();
      } catch (_error) {
        // 合并失败，忽略
      }
    }

    // 为每个日期生成任务数据
    allDates.forEach((serial, dateIndex) => {
      const tasks = personData.get(serial) || [];
      const colOffset = dateIndex * config.structure.columnsPerDate + 1;

      if (tasks.length === 0) {
        // 没有任务的日期，显示空值
        for (let i = 0; i < config.structure.columnsPerDate; i++) {
          sheet.getCell(currentRow, colOffset + i).setValue("");
        }
      } else {
        // 计算当天总工时和折算比例
        const dayTotal = tasks.reduce((sum, task) => sum + (parseFloat(task.workHours) || 0), 0);
        const scaleFactor = dayTotal > CONFIG.global.workHours.maxDailyHours ?
          (CONFIG.global.workHours.maxDailyHours / dayTotal) : 1;

        // 按行业排序任务
        const sortedTasks = [...tasks].sort((a, b) => {
          const ai = String(a.industry || '').trim().toLowerCase();
          const bi = String(b.industry || '').trim().toLowerCase();
          return ai.localeCompare(bi);
        });

        // 写入任务行
        sortedTasks.forEach((task, taskIndex) => {
          const rowIndex = currentRow + taskIndex;
          const singleHours = parseFloat(task.workHours) || 0;
          const convertedHours = singleHours * scaleFactor;

          sheet.getCell(rowIndex, colOffset).setValue(task.taskDescription);
          sheet.getCell(rowIndex, colOffset + 1).setValue(singleHours);
          sheet.getCell(rowIndex, colOffset + 2).setValue(convertedHours);
          sheet.getCell(rowIndex, colOffset + 3).setValue(String(task.industry || ""));

          // 行业合计工时和合计工时只在第一行显示
          if (taskIndex === 0) {
            sheet.getCell(rowIndex, colOffset + 4).setValue(dayTotal);
            sheet.getCell(rowIndex, colOffset + 5).setValue(dayTotal);

            // 超时高亮
            if (CONFIG.global.workHours.highlightOvertime && dayTotal > CONFIG.global.workHours.maxDailyHours) {
              try {
                sheet.getCell(rowIndex, colOffset + 4).setFontColor(CONFIG.global.workHours.overtimeColor);
                sheet.getCell(rowIndex, colOffset + 4).setFontWeight('bold');
                sheet.getCell(rowIndex, colOffset + 5).setFontColor(CONFIG.global.workHours.overtimeColor);
                sheet.getCell(rowIndex, colOffset + 5).setFontWeight('bold');
              } catch (_error) {
                // 样式设置失败，忽略
              }
            }
          }
        });
      }
    });

    currentRow += maxRowsForPerson;
  });
}

/**
 * 生成行业日历表格
 * @param {Sheet} sheet - 工作表对象
 * @param {Map} industryStats - 行业统计数据
 * @param {Array} sortedDates - 排序后的日期
 * @param {Object} config - 配置对象
 */
function generateIndustryCalendarTable(sheet, industryStats, sortedDates, config) {
  let currentRow = 0;

  // 第一行：日期（跨两列合并）
  sortedDates.forEach((serial, index) => {
    const colOffset = index * config.structure.columnsPerDate + 1;
    const dateCell = sheet.getCell(currentRow, colOffset);
    dateCell.setValue(serial);
    dateCell.setNumberFormat(config.styles.dateFormat);

    try {
      const mergeRange = sheet.getRange(currentRow, colOffset, 1, config.structure.columnsPerDate);
      mergeRange.merge();
    } catch (_error) {
      // 合并失败，忽略
    }
  });
  currentRow++;

  // 第二行：左侧行业标题 + 子列标题
  sheet.getCell(currentRow, 0).setValue("行业");
  sortedDates.forEach((_, index) => {
    const colOffset = index * config.structure.columnsPerDate + 1;
    config.structure.columns.forEach((col, colIndex) => {
      sheet.getCell(currentRow, colOffset + colIndex).setValue(col.name);
    });
  });
  currentRow++;

  // 数据区
  const industries = Array.from(industryStats.keys()).sort();
  industries.forEach(industry => {
    const dateMap = industryStats.get(industry) || new Map();
    sheet.getCell(currentRow, 0).setValue(industry);

    sortedDates.forEach((serial, index) => {
      const stat = dateMap.get(serial) || { headcount: 0, hours: 0 };
      const colOffset = index * config.structure.columnsPerDate + 1;
      sheet.getCell(currentRow, colOffset).setValue(stat.headcount);
      sheet.getCell(currentRow, colOffset + 1).setValue(stat.hours);
    });

    currentRow++;
  });
}

/**
 * 应用个人日历样式
 * @param {Sheet} sheet - 工作表对象
 * @param {Array} allDates - 所有日期
 * @param {Object} config - 配置对象
 */
function applyPersonalCalendarStyles(sheet, allDates, config) {
  try {
    const totalCols = allDates.length * config.structure.columnsPerDate + 1;

    // 设置列宽
    sheet.setColumnWidth(0, config.styles.nameColumnWidth);
    for (let i = 1; i < totalCols; i++) {
      const colType = (i - 1) % config.structure.columnsPerDate;
      const colConfig = config.structure.columns[colType];
      if (colConfig && colConfig.width) {
        sheet.setColumnWidth(i, colConfig.width);
      }
    }

    // 设置标题行样式
    const headerRange1 = sheet.getRange(0, 0, 1, totalCols);
    const headerRange2 = sheet.getRange(1, 0, 1, totalCols);

    try {
      headerRange1.setFontWeight(config.styles.headerFontWeight);
      headerRange1.setBackgroundColor(config.styles.headerBackgroundColor);
      headerRange1.setFontColor(config.styles.headerFontColor);

      headerRange2.setFontWeight(config.styles.headerFontWeight);
      headerRange2.setBackgroundColor(config.styles.headerBackgroundColor);
      headerRange2.setFontColor(config.styles.headerFontColor);
    } catch (_error) {
      // 样式设置失败，忽略
    }

  } catch (error) {
    console.log("[applyPersonalCalendarStyles] 应用样式时出错:", error.message);
  }
}

/**
 * 应用行业日历样式
 * @param {Sheet} sheet - 工作表对象
 * @param {Array} sortedDates - 排序后的日期
 * @param {Object} config - 配置对象
 */
function applyIndustryCalendarStyles(sheet, sortedDates, config) {
  try {
    const totalCols = sortedDates.length * config.structure.columnsPerDate + 1;

    // 设置列宽
    sheet.setColumnWidth(0, config.styles.industryColumnWidth);
    for (let i = 0; i < sortedDates.length; i++) {
      config.structure.columns.forEach((col, colIndex) => {
        const actualCol = i * config.structure.columnsPerDate + 1 + colIndex;
        if (col.width) {
          sheet.setColumnWidth(actualCol, col.width);
        }
      });
    }

    // 设置标题行样式
    const headerRange1 = sheet.getRange(0, 0, 1, totalCols);
    const headerRange2 = sheet.getRange(1, 0, 1, totalCols);

    try {
      headerRange1.setFontWeight(config.styles.headerFontWeight);
      headerRange1.setBackgroundColor(config.styles.headerBackgroundColor);
      headerRange1.setFontColor(config.styles.headerFontColor);

      headerRange2.setFontWeight(config.styles.headerFontWeight);
      headerRange2.setBackgroundColor(config.styles.headerBackgroundColor);
      headerRange2.setFontColor(config.styles.headerFontColor);
    } catch (_error) {
      // 样式设置失败，忽略
    }

  } catch (error) {
    console.log("[applyIndustryCalendarStyles] 应用样式时出错:", error.message);
  }
}

// 启动脚本
await main();

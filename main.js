/**
 * 钉钉文档脚本 - 测试人力计划解析器（优化版）
 * 解析多种类型的工作表，提取每个人每天的工作内容和工时
 * 支持配置驱动的数据源处理和输出格式定制
 */

// ==================== 主配置对象 ====================

const CONFIG = {
  // 输入数据源配置
  input: {
    dataSources: [
      {
        sheetName: "测试任务日历",
        type: "testTask",
        dateRow: 1, // 日期所在行（从0开始）
        dataStartRow: 2, // 数据开始行（从0开始）
        industryColumn: 0, // 行业列索引（第A列）
        columns: {
          projectIteration: "项目_迭代", // 支持模糊匹配
          taskName: "事项名称",
          taskDescription: "任务描述",
          testPersonnel: "测试人员"
        },
        processing: {
          descriptionPreference: "taskName", // 优先使用任务名称
          industryFromColumn: true, // 从列中获取行业信息
          defaultIndustry: "未知行业"
        }
      },
      {
        sheetName: "公共事项日历",
        type: "publicEvent",
        dateRow: 0, // 日期所在行（从0开始）
        dataStartRow: 1, // 数据开始行（从0开始）
        industryColumn: null, // 没有独立的行业列
        columns: {
          projectIteration: "项目_迭代",
          taskName: "事项名称",
          taskDescription: "任务描述",
          testPersonnel: "测试人员"
        },
        processing: {
          descriptionPreference: "taskDescription", // 优先使用任务描述
          industryFromColumn: false,
          industryFromContent: true, // 从事项内容中提取行业
          defaultIndustry: "公共事项"
        }
      }
    ],
    // 通用处理配置
    processing: {
      personnelSeparators: /[,，、\s.。;；|]+/, // 人员分隔符
      supportMergedCells: true, // 支持合并单元格
      dateRange: {
        minSerial: 40000, // Excel日期序列号最小值
        maxSerial: 80000  // Excel日期序列号最大值
      }
    }
  },

  // 输出配置
  output: {
    // 个人工时日历
    personalTaskCalendar: {
      sheetName: "个人工时日历(Auto)",
      enabled: true,
      columnsPerDate: 6,
      columns: [
        { name: "任务描述", width: 200 },
        { name: "单项工时(小时)", width: 120, format: "0.00" },
        { name: "折算工时(小时)", width: 120, format: "0.00" },
        { name: "行业", width: 120 },
        { name: "行业合计工时(小时)", width: 120, format: "0.00" },
        { name: "合计工时(小时)", width: 120, format: "0.00", highlight: true }
      ]
    },

    // 行业工时日历
    industryWorkCalendar: {
      sheetName: "行业工时日历(Auto)",
      enabled: true,
      columnsPerDate: 2,
      columns: [
        { name: "人力", width: 120, format: "0" },
        { name: "工时(小时)", width: 120, format: "0.00" }
      ]
    }
  },

  // 样式配置（复用）
  styles: {
    // 颜色配置
    colors: {
      headerBackground: '#0171C1',
      headerFont: '#FFFFFF',
      leftColumnBackground: '#92D04F',
      alternateRowBackground: '#FAFAFA',
      highlightBackground: '#FFF2CC',
      overtimeFont: '#FF0000',
      borderColor: '#000000'
    },
    // 尺寸配置
    dimensions: {
      nameColumnWidth: 80,
      industryColumnWidth: 120,
      taskColumnWidth: 200,
      hourColumnWidth: 120,
      headcountColumnWidth: 120,
      rowHeight: 25
    },
    // 格式配置
    formats: {
      datePersonal: 'm"月"d"日"',
      dateIndustry: 'm"/"d',
      hours: '0.00',
      count: '0'
    },
    // 冻结窗格
    freezePanes: {
      rows: 2,
      columns: 1
    }
  },

  // 全局配置
  global: {
    workHours: {
      maxDailyHours: 8, // 每日最大工时，超过则按比例折算
      highlightOvertime: true // 是否高亮显示超时
    },
    logging: {
      enabled: true,
      level: "info" // debug, info, warn, error
    }
  }
};

// ==================== 主函数 ====================

async function main() {
  console.log("[main] 🚀 开始执行脚本（优化版）");

  try {
    // 获取所有工作表
    const sheets = Workbook.getSheets();
    console.log("[main] 获取到工作表:", sheets.map(sheet => sheet.getName()));

    if (sheets.length === 0) {
      Output.error("[main] 错误：工作簿中没有找到任何工作表");
      return;
    }

    // 处理所有配置的数据源
    const allAnalyses = [];

    for (const sourceConfig of CONFIG.input.dataSources) {
      const analysis = await processDataSource(sourceConfig);
      if (analysis) {
        allAnalyses.push(analysis);
      }
    }

    console.log("[main] 数据源解析完成，共处理", allAnalyses.length, "个数据源");

    // 合并所有数据源的结果
    const mergedAnalysis = mergeAnalyses(allAnalyses);
    console.log("[main] 数据合并完成，共", mergedAnalysis.dailyWorkAnalysis.length, "个工作日");

    // 输出统计信息
    if (mergedAnalysis.dailyWorkAnalysis.length > 0) {
      outputStatistics(mergedAnalysis);

      // 生成个人工时日历
      if (CONFIG.output.personalTaskCalendar.enabled) {
        generatePersonalTaskCalendar(mergedAnalysis.dailyWorkAnalysis);
        console.log("[main] 个人工时日历生成完成");
      }

      // 生成行业工时日历
      if (CONFIG.output.industryWorkCalendar.enabled) {
        generateIndustryWorkCalendar(mergedAnalysis.dailyWorkAnalysis);
        console.log("[main] 行业工时日历生成完成");
      }

      Output.log("[main] === 所有任务完成 ===");
    } else {
      Output.log("[main] 未找到有效数据，跳过输出生成");
    }

  } catch (error) {
    Output.error(`[main] 脚本执行出错: ${error.message}`);
    if (error.stack) {
      Output.error(`[main] 错误堆栈: ${error.stack}`);
    }
  }

  console.log("[main] 🚀 脚本执行结束");
}

// ==================== 核心数据处理函数 ====================

/**
 * 处理单个数据源
 * @param {Object} sourceConfig - 数据源配置
 * @returns {Object|null} 解析后的数据分析结果
 */
async function processDataSource(sourceConfig) {
  try {
    // 尝试获取工作表
    let sheet;
    try {
      sheet = Workbook.getSheet(sourceConfig.sheetName);
    } catch (_error) {
      console.log(`[processDataSource] 工作表 "${sourceConfig.sheetName}" 不存在，跳过处理`);
      return null;
    }

    console.log(`[processDataSource] 开始处理数据源: ${sourceConfig.sheetName}`);

    // 获取工作表数据
    const sheetData = getSheetData(sheet);
    if (!sheetData) {
      return { dailyWorkAnalysis: [] };
    }

    // 解析表头，获取列映射
    const columnMap = parseHeaders(sheetData.values, sourceConfig);
    if (!columnMap.dateColumns.length) {
      Output.error(`[processDataSource] 未找到有效的日期列`);
      return { dailyWorkAnalysis: [] };
    }

    // 解析数据行
    const dailyWorkMap = new Map();
    let validRows = 0;
    let lastProjectIteration = "";
    let lastIndustry = "";

    for (let i = sourceConfig.dataStartRow; i < sheetData.values.length; i++) {
      const row = sheetData.values[i];

      // 跳过空行
      if (isEmptyRow(row)) continue;

      // 提取任务信息
      const taskInfo = extractTaskInfo(row, columnMap, sourceConfig, lastProjectIteration);
      if (!taskInfo.taskDescription) continue;

      // 提取行业信息
      let industry = sourceConfig.processing.defaultIndustry;
      if (sourceConfig.processing.industryFromColumn && sourceConfig.industryColumn !== null) {
        const currentIndustry = String(row[sourceConfig.industryColumn] || "").trim();
        industry = currentIndustry || lastIndustry || sourceConfig.processing.defaultIndustry;
        if (currentIndustry) {
          lastIndustry = currentIndustry;
        }
      } else if (sourceConfig.processing.industryFromContent && taskInfo.taskDescription) {
        const parts = taskInfo.taskDescription.split(' - ');
        if (parts.length > 1) {
          industry = parts[1].trim();
        }
      }

      if (taskInfo.currentProjectIteration) {
        lastProjectIteration = taskInfo.currentProjectIteration;
      }

      validRows++;

      // 处理每个日期列的工时数据
      columnMap.dateColumns.forEach(dateCol => {
        const workHours = parseFloat(row[dateCol.index]) || 0;
        if (workHours > 0) {
          addTaskToDaily(dailyWorkMap, dateCol.serial, {
            industry,
            taskDescription: taskInfo.taskDescription,
            testPersonnel: taskInfo.testPersonnel,
            workHours
          });
        }
      });
    }

    console.log(`[processDataSource] 数据源 "${sourceConfig.sheetName}" 处理完成，有效行数: ${validRows}，工作日数: ${dailyWorkMap.size}`);

    return {
      dailyWorkAnalysis: Array.from(dailyWorkMap.values()).sort((a, b) => a.dateSerial - b.dateSerial)
    };

  } catch (error) {
    Output.error(`[processDataSource] 处理数据源 "${sourceConfig.sheetName}" 时出错: ${error.message}`);
    return null;
  }
}

// ==================== 辅助函数 ====================

/**
 * 获取工作表数据
 * @param {Sheet} sheet - 工作表对象
 * @returns {Object|null} 包含values数组的对象，或null
 */
function getSheetData(sheet) {
  try {
    // 尝试获取较大范围的数据
    const dataRange = sheet.getRange("A1:AZ1000");
    const values = dataRange.getValues();

    // 移除完全空的行
    while (values.length > 0 && values[values.length - 1].every(cell => !cell || String(cell).trim() === "")) {
      values.pop();
    }

    if (values.length === 0) {
      Output.error("[getSheetData] 工作表为空");
      return null;
    }

    return { values };
  } catch (error) {
    Output.error(`[getSheetData] 获取工作表数据失败: ${error.message}`);
    return null;
  }
}

/**
 * 解析表头，识别各列位置
 * @param {Array} values - 工作表数据
 * @param {Object} config - 配置对象
 * @returns {Object} 列映射对象
 */
function parseHeaders(values, config) {
  const columnMap = {
    projectIteration: -1,
    taskName: -1,
    taskDescription: -1,
    testPersonnel: -1,
    dateColumns: []
  };

  if (values.length <= config.dateRow) {
    Output.error("[parseHeaders] 数据行数不足，无法解析表头");
    return columnMap;
  }

  const headerRow1 = values[0];
  const dateHeaderRow = values[config.dateRow];
  const numCols = Math.max(headerRow1.length, dateHeaderRow.length);

  // 解析常规列（使用模糊匹配）
  for (let index = 0; index < numCols; index++) {
    const header1Str = String(headerRow1[index] || "").trim().toLowerCase();

    // 匹配项目迭代列
    if (config.columns.projectIteration && header1Str.includes(config.columns.projectIteration.toLowerCase())) {
      columnMap.projectIteration = index;
    }

    // 匹配任务名称列
    if (config.columns.taskName && header1Str.includes(config.columns.taskName.toLowerCase())) {
      columnMap.taskName = index;
    }

    // 匹配任务描述列
    if (config.columns.taskDescription && header1Str.includes(config.columns.taskDescription.toLowerCase())) {
      columnMap.taskDescription = index;
    }

    // 匹配测试人员列
    if (config.columns.testPersonnel && header1Str.includes(config.columns.testPersonnel.toLowerCase())) {
      columnMap.testPersonnel = index;
    }
  }

  // 解析日期列
  for (let index = 0; index < dateHeaderRow.length; index++) {
    const dateHeaderValue = dateHeaderRow[index];
    if (typeof dateHeaderValue === 'number' && Number.isFinite(dateHeaderValue)) {
      if (dateHeaderValue > CONFIG.input.processing.dateRange.minSerial &&
        dateHeaderValue < CONFIG.input.processing.dateRange.maxSerial) {
        columnMap.dateColumns.push({ index, serial: Math.round(dateHeaderValue) });
      }
    }
  }

  console.log(`[parseHeaders] 找到 ${columnMap.dateColumns.length} 个日期列`);
  return columnMap;
}

/**
 * 提取任务基本信息
 * @param {Array} row - 数据行
 * @param {Object} columnMap - 列映射
 * @param {Object} config - 配置对象
 * @param {string} lastProjectIteration - 上一行的项目迭代信息
 * @returns {Object} 任务信息
 */
function extractTaskInfo(row, columnMap, config, lastProjectIteration = "") {
  // 提取当前行的项目迭代信息
  const currentProjectIteration = columnMap.projectIteration >= 0 ?
    String(row[columnMap.projectIteration] || "").trim() : "";

  // 决定使用哪个项目迭代信息
  const projectIteration = currentProjectIteration || lastProjectIteration;

  // 提取基础描述字段
  const taskName = columnMap.taskName >= 0 ? String(row[columnMap.taskName] || "").trim() : "";
  const taskDescField = columnMap.taskDescription >= 0 ? String(row[columnMap.taskDescription] || "").trim() : "";

  const baseDescription = (config.processing.descriptionPreference === 'taskDescription')
    ? (taskDescField || taskName)
    : (taskName || taskDescField);

  // 拼接任务描述
  let taskDescription = "";
  if (projectIteration && baseDescription) {
    taskDescription = `${projectIteration} - ${baseDescription}`;
  } else if (projectIteration) {
    taskDescription = projectIteration;
  } else if (baseDescription) {
    taskDescription = baseDescription;
  }

  // 提取测试人员
  const testPersonnelStr = columnMap.testPersonnel >= 0 ?
    String(row[columnMap.testPersonnel] || "").trim() : "";

  const testPersonnel = testPersonnelStr ?
    testPersonnelStr.split(CONFIG.input.processing.personnelSeparators).filter(name => name.length > 0) : [];

  return {
    taskDescription,
    testPersonnel,
    currentProjectIteration
  };
}

/**
 * 添加任务到每日工作映射
 * @param {Map} dailyWorkMap - 每日工作映射
 * @param {number} dateSerial - 日期序列号
 * @param {Object} task - 任务对象
 */
function addTaskToDaily(dailyWorkMap, dateSerial, task) {
  if (!dailyWorkMap.has(dateSerial)) {
    dailyWorkMap.set(dateSerial, { dateSerial, tasks: [] });
  }
  dailyWorkMap.get(dateSerial).tasks.push(task);
}

/**
 * 合并多个分析结果
 * @param {Array} analyses - 分析结果数组
 * @returns {Object} 合并后的结果
 */
function mergeAnalyses(analyses) {
  const mergedMap = new Map();

  analyses.filter(Boolean).forEach(analysis => {
    if (analysis?.dailyWorkAnalysis) {
      analysis.dailyWorkAnalysis.forEach(day => {
        const dateKey = day.dateSerial;
        if (!mergedMap.has(dateKey)) {
          mergedMap.set(dateKey, { dateSerial: dateKey, tasks: [] });
        }
        const entry = mergedMap.get(dateKey);
        if (Array.isArray(day.tasks) && day.tasks.length > 0) {
          entry.tasks.push(...day.tasks);
        }
      });
    }
  });

  const dailyWorkAnalysis = Array.from(mergedMap.values())
    .sort((a, b) => a.dateSerial - b.dateSerial);

  return { dailyWorkAnalysis };
}

/**
 * 判断是否为空行
 * @param {Array} row - 数据行
 * @returns {boolean} 是否为空行
 */
function isEmptyRow(row) {
  return row.every(cell => {
    const cellValue = String(cell || "").trim();
    return cellValue === "" || cellValue === "0";
  });
}

/**
 * 将 Excel 日期序列号转换为 JS Date 对象
 * @param {number} serial - Excel 日期序列号
 * @returns {Date | null} JS Date 对象或 null
 */
function excelSerialToJsDate(serial) {
  if (typeof serial !== 'number' || !Number.isFinite(serial)) return null;
  const dayMs = 24 * 60 * 60 * 1000;
  const excelEpochUTC = Date.UTC(1899, 11, 30); // 1899-12-30
  const millis = excelEpochUTC + Math.round(serial) * dayMs;
  return new Date(millis);
}

/**
 * 格式化日期序列号用于显示
 * @param {number} serial - Excel 日期序列号
 * @returns {string} 格式化后的日期字符串
 */
function formatDateSerialForDisplay(serial) {
  const date = excelSerialToJsDate(serial);
  if (!date) return "";
  const month = date.getUTCMonth() + 1;
  const day = date.getUTCDate();
  return `${month}/${day}`;
}

// ==================== 统计和输出函数 ====================

/**
 * 输出统计信息
 * @param {Object} workAnalysis - 工作分析数据
 */
function outputStatistics(workAnalysis) {
  Output.log("[outputStatistics] === 统计信息 ===");

  const { dailyWorkAnalysis } = workAnalysis;

  if (dailyWorkAnalysis.length === 0) {
    Output.error("[outputStatistics] 无统计数据");
    return;
  }

  // 总体统计
  const totalDays = dailyWorkAnalysis.length;
  const totalTasks = dailyWorkAnalysis.reduce((sum, day) => sum + day.tasks.length, 0);
  const totalWorkHours = dailyWorkAnalysis.reduce((sum, day) =>
    sum + day.tasks.reduce((daySum, task) => daySum + task.workHours, 0), 0);

  Output.log(`[outputStatistics] 总天数: ${totalDays}`);
  Output.log(`[outputStatistics] 总任务数: ${totalTasks}`);
  Output.log(`[outputStatistics] 总工时: ${totalWorkHours.toFixed(1)} 小时`);

  // 人员工时统计
  const personnelStats = new Map();
  dailyWorkAnalysis.forEach(day => {
    day.tasks.forEach(task => {
      task.testPersonnel.forEach(person => {
        if (!personnelStats.has(person)) {
          personnelStats.set(person, 0);
        }
        personnelStats.set(person, personnelStats.get(person) + task.workHours);
      });
    });
  });

  if (personnelStats.size > 0) {
    Output.log("[outputStatistics] 人员工时统计:");
    Array.from(personnelStats.entries())
      .sort((a, b) => b[1] - a[1])
      .forEach(([person, hours]) => {
        Output.log(`[outputStatistics] 人员工时, person: ${person}, hours: ${hours.toFixed(1)}`);
      });
  }

  // 每日工时统计
  Output.log("[outputStatistics] 每日工时统计:");
  dailyWorkAnalysis.forEach(day => {
    const dayTotal = day.tasks.reduce((sum, task) => sum + task.workHours, 0);
    Output.log(`[outputStatistics] 每日工时, date: ${formatDateSerialForDisplay(day.dateSerial)}, hours: ${dayTotal.toFixed(1)}, tasks: ${day.tasks.length}`);
  });
}

/**
 * 生成个人工时日历
 * @param {Array} dailyWorkAnalysis - 每日工作分析数据
 */
function generatePersonalTaskCalendar(dailyWorkAnalysis) {
  try {
    const config = CONFIG.output.personalTaskCalendar;

    // 检查并删除已存在的工作表
    let sheet;
    try {
      const existingSheet = Workbook.getSheet(config.sheetName);
      if (existingSheet) {
        Workbook.deleteSheet(existingSheet);
      }
    } catch (_error) {
      // 工作表不存在，忽略错误
    }

    // 创建新工作表
    sheet = Workbook.insertSheet(config.sheetName);
    if (!sheet) {
      Output.error(`[generatePersonalTaskCalendar] 无法创建工作表: ${config.sheetName}`);
      return;
    }

    // 获取所有日期并排序
    const allDates = Array.from(new Set(dailyWorkAnalysis.map(day => day.dateSerial)))
      .sort((a, b) => a - b);

    // 构建人员任务数据结构
    const personnelTaskData = buildPersonnelTaskData(dailyWorkAnalysis);

    // 生成表格并获取实际行数
    const actualRows = generatePersonalCalendarTable(sheet, personnelTaskData, allDates, config);

    // 应用样式（传递实际行数）
    applyPersonalCalendarStyles(sheet, allDates, config, actualRows);

    // 冻结窗格
    applyFreezePanes(sheet, CONFIG.styles.freezePanes.rows, CONFIG.styles.freezePanes.columns);

    Output.log("[generatePersonalTaskCalendar] 个人工时日历生成完成");

  } catch (error) {
    Output.error(`[generatePersonalTaskCalendar] 生成个人工时日历时出错: ${error.message}`);
  }
}

/**
 * 生成行业工时日历
 * @param {Array} dailyWorkAnalysis - 每日工作分析数据
 */
function generateIndustryWorkCalendar(dailyWorkAnalysis) {
  try {
    const config = CONFIG.output.industryWorkCalendar;

    // 检查并删除已存在的工作表
    let sheet;
    try {
      const existingSheet = Workbook.getSheet(config.sheetName);
      if (existingSheet) {
        Workbook.deleteSheet(existingSheet);
      }
    } catch (_error) {
      // 工作表不存在，忽略错误
    }

    // 创建新工作表
    sheet = Workbook.insertSheet(config.sheetName);
    if (!sheet) {
      Output.error(`[generateIndustryWorkCalendar] 无法创建工作表: ${config.sheetName}`);
      return;
    }

    // 计算行业统计数据
    const industryStats = calculateIndustryStats(dailyWorkAnalysis);

    // 获取所有日期并排序
    const allDatesSet = new Set();
    industryStats.forEach(dateMap => {
      dateMap.forEach((_val, dateKey) => allDatesSet.add(Number(dateKey)));
    });
    const sortedDates = Array.from(allDatesSet).sort((a, b) => a - b);

    // 生成表格并获取实际行数
    const actualRows = generateIndustryCalendarTable(sheet, industryStats, sortedDates, config);

    // 应用样式（传递实际行数）
    applyIndustryCalendarStyles(sheet, sortedDates, config, actualRows);

    // 冻结窗格
    applyFreezePanes(sheet, CONFIG.styles.freezePanes.rows, CONFIG.styles.freezePanes.columns);

    Output.log("[generateIndustryWorkCalendar] 行业工时日历生成完成");

  } catch (error) {
    Output.error(`[generateIndustryWorkCalendar] 生成行业工时日历时出错: ${error.message}`);
  }
}

// ==================== 表格生成辅助函数 ====================

/**
 * 构建人员任务数据结构
 * @param {Array} dailyWorkAnalysis - 每日工作分析数据
 * @returns {Map} 人员任务数据映射
 */
function buildPersonnelTaskData(dailyWorkAnalysis) {
  const personnelTaskData = new Map(); // Map<Person, Map<dateSerial, Task[]>>

  dailyWorkAnalysis.forEach(day => {
    const dateKey = day.dateSerial;
    day.tasks.forEach(task => {
      task.testPersonnel.forEach(person => {
        if (!personnelTaskData.has(person)) {
          personnelTaskData.set(person, new Map());
        }
        const personMap = personnelTaskData.get(person);
        if (!personMap.has(dateKey)) {
          personMap.set(dateKey, []);
        }

        personMap.get(dateKey).push({
          taskDescription: task.taskDescription,
          workHours: task.workHours,
          industry: task.industry
        });
      });
    });
  });

  return personnelTaskData;
}

/**
 * 计算行业统计数据
 * @param {Array} dailyWorkAnalysis - 每日工作分析数据
 * @returns {Map} 行业统计数据映射
 */
function calculateIndustryStats(dailyWorkAnalysis) {
  const tempMap = new Map(); // Map<Industry, Map<DateKey, { people: Set<string>, hours: number }>>

  dailyWorkAnalysis.forEach(day => {
    const dateKey = day.dateSerial;
    day.tasks.forEach(task => {
      if (!task.industry) return;

      if (!tempMap.has(task.industry)) {
        tempMap.set(task.industry, new Map());
      }
      const industryDateMap = tempMap.get(task.industry);

      if (!industryDateMap.has(dateKey)) {
        industryDateMap.set(dateKey, { people: new Set(), hours: 0 });
      }
      const entry = industryDateMap.get(dateKey);

      // 工时累计
      entry.hours += (parseFloat(task.workHours) || 0);

      // 人员集合
      if (Array.isArray(task.testPersonnel)) {
        task.testPersonnel.forEach(person => {
          const name = String(person || '').trim();
          if (name) entry.people.add(name);
        });
      }
    });
  });

  // 转换为最终格式
  const result = new Map(); // Map<Industry, Map<DateKey, { headcount, hours }>>
  tempMap.forEach((dateMap, industry) => {
    const outDateMap = new Map();
    dateMap.forEach((entry, dateKey) => {
      outDateMap.set(dateKey, { headcount: entry.people.size, hours: entry.hours });
    });
    result.set(industry, outDateMap);
  });

  return result;
}

/**
 * 生成个人日历表格
 * @param {Sheet} sheet - 工作表对象
 * @param {Map} personnelTaskData - 人员任务数据
 * @param {Array} allDates - 所有日期
 * @param {Object} config - 配置对象
 * @returns {number} 实际使用的总行数
 */
function generatePersonalCalendarTable(sheet, personnelTaskData, allDates, config) {
  let currentRow = 0;

  // 生成日期标题行
  allDates.forEach((serial, index) => {
    const colOffset = index * config.columnsPerDate + 1;
    const dateCell = sheet.getCell(currentRow, colOffset);
    dateCell.setValue(serial);
    dateCell.setNumberFormat(CONFIG.styles.formats.datePersonal);

    // 合并日期标题单元格
    try {
      const dateHeaderRange = sheet.getRange(currentRow, colOffset, 1, config.columnsPerDate);
      dateHeaderRange.merge();
    } catch (_error) {
      // 合并失败，忽略
    }
  });
  currentRow++;

  // 生成列标题行
  sheet.getCell(currentRow, 0).setValue("姓名");
  allDates.forEach((_, index) => {
    const colOffset = index * config.columnsPerDate + 1;
    config.columns.forEach((col, colIndex) => {
      sheet.getCell(currentRow, colOffset + colIndex).setValue(col.name);
    });
  });
  currentRow++;

  // 生成人员数据
  const sortedPersonnel = Array.from(personnelTaskData.keys()).sort();
  sortedPersonnel.forEach(person => {
    const personData = personnelTaskData.get(person);
    let maxRowsForPerson = 1;

    // 计算这个人需要的最大行数
    allDates.forEach(serial => {
      const tasks = personData.get(serial) || [];
      maxRowsForPerson = Math.max(maxRowsForPerson, tasks.length);
    });

    // 设置人员姓名
    sheet.getCell(currentRow, 0).setValue(person);
    if (maxRowsForPerson > 1) {
      try {
        const nameRange = sheet.getRange(currentRow, 0, maxRowsForPerson, 1);
        nameRange.merge();
      } catch (_error) {
        // 合并失败，忽略
      }
    }

    // 为每个日期生成任务数据
    allDates.forEach((serial, dateIndex) => {
      const tasks = personData.get(serial) || [];
      const colOffset = dateIndex * config.columnsPerDate + 1;

      if (tasks.length === 0) {
        // 没有任务的日期，显示空值
        for (let i = 0; i < config.columnsPerDate; i++) {
          sheet.getCell(currentRow, colOffset + i).setValue("");
        }
      } else {
        // 计算当天总工时和折算比例
        const dayTotal = tasks.reduce((sum, task) => sum + (parseFloat(task.workHours) || 0), 0);
        const scaleFactor = dayTotal > CONFIG.global.workHours.maxDailyHours ?
          (CONFIG.global.workHours.maxDailyHours / dayTotal) : 1;

        // 按行业排序任务
        const sortedTasks = [...tasks].sort((a, b) => {
          const ai = String(a.industry || '').trim().toLowerCase();
          const bi = String(b.industry || '').trim().toLowerCase();
          return ai.localeCompare(bi);
        });

        // 写入任务行
        sortedTasks.forEach((task, taskIndex) => {
          const rowIndex = currentRow + taskIndex;
          const singleHours = parseFloat(task.workHours) || 0;
          const convertedHours = singleHours * scaleFactor;

          sheet.getCell(rowIndex, colOffset).setValue(task.taskDescription);
          sheet.getCell(rowIndex, colOffset + 1).setValue(singleHours);
          sheet.getCell(rowIndex, colOffset + 2).setValue(convertedHours);
          sheet.getCell(rowIndex, colOffset + 3).setValue(String(task.industry || ""));

          // 行业合计工时和合计工时只在第一行显示
          if (taskIndex === 0) {
            sheet.getCell(rowIndex, colOffset + 4).setValue(dayTotal);
            sheet.getCell(rowIndex, colOffset + 5).setValue(dayTotal);

            // 超时高亮
            if (CONFIG.global.workHours.highlightOvertime && dayTotal > CONFIG.global.workHours.maxDailyHours) {
              try {
                sheet.getCell(rowIndex, colOffset + 4).setFontColor(CONFIG.styles.colors.overtimeFont);
                sheet.getCell(rowIndex, colOffset + 4).setFontWeight('bold');
                sheet.getCell(rowIndex, colOffset + 5).setFontColor(CONFIG.styles.colors.overtimeFont);
                sheet.getCell(rowIndex, colOffset + 5).setFontWeight('bold');
              } catch (_error) {
                // 样式设置失败，忽略
              }
            }
          }
        });
      }
    });

    currentRow += maxRowsForPerson;
  });

  return currentRow; // 返回实际使用的总行数
}

/**
 * 生成行业日历表格
 * @param {Sheet} sheet - 工作表对象
 * @param {Map} industryStats - 行业统计数据
 * @param {Array} sortedDates - 排序后的日期
 * @param {Object} config - 配置对象
 * @returns {number} 实际使用的总行数
 */
function generateIndustryCalendarTable(sheet, industryStats, sortedDates, config) {
  let currentRow = 0;

  // 第一行：日期（跨两列合并）
  sortedDates.forEach((serial, index) => {
    const colOffset = index * config.columnsPerDate + 1;
    const dateCell = sheet.getCell(currentRow, colOffset);
    dateCell.setValue(serial);
    dateCell.setNumberFormat(CONFIG.styles.formats.dateIndustry);

    try {
      const mergeRange = sheet.getRange(currentRow, colOffset, 1, config.columnsPerDate);
      mergeRange.merge();
    } catch (_error) {
      // 合并失败，忽略
    }
  });
  currentRow++;

  // 第二行：左侧行业标题 + 子列标题
  sheet.getCell(currentRow, 0).setValue("行业");
  sortedDates.forEach((_, index) => {
    const colOffset = index * config.columnsPerDate + 1;
    config.columns.forEach((col, colIndex) => {
      sheet.getCell(currentRow, colOffset + colIndex).setValue(col.name);
    });
  });
  currentRow++;

  // 数据区
  const industries = Array.from(industryStats.keys()).sort();
  industries.forEach(industry => {
    const dateMap = industryStats.get(industry) || new Map();
    sheet.getCell(currentRow, 0).setValue(industry);

    sortedDates.forEach((serial, index) => {
      const stat = dateMap.get(serial) || { headcount: 0, hours: 0 };
      const colOffset = index * config.columnsPerDate + 1;
      sheet.getCell(currentRow, colOffset).setValue(stat.headcount);
      sheet.getCell(currentRow, colOffset + 1).setValue(stat.hours);
    });

    currentRow++;
  });

  return currentRow; // 返回实际使用的总行数
}

// ==================== 样式应用函数 ====================

/**
 * 应用个人日历样式
 * @param {Sheet} sheet - 工作表对象
 * @param {Array} allDates - 所有日期
 * @param {Object} config - 配置对象
 * @param {number} totalRows - 实际数据行数
 */
function applyPersonalCalendarStyles(sheet, allDates, config, totalRows) {
  try {
    const totalCols = allDates.length * config.columnsPerDate + 1;

    // 设置列宽
    sheet.setColumnWidth(0, CONFIG.styles.dimensions.nameColumnWidth);
    for (let i = 1; i < totalCols; i++) {
      const colType = (i - 1) % config.columnsPerDate;
      const colConfig = config.columns[colType];
      if (colConfig?.width) {
        sheet.setColumnWidth(i, colConfig.width);
      }
    }

    // 设置标题行样式
    applyHeaderStyles(sheet, 0, 0, 1, totalCols);
    applyHeaderStyles(sheet, 1, 0, 1, totalCols);

    // 设置左侧列样式（只对数据行，不覆盖标题行）
    applyLeftColumnStyles(sheet, totalRows);

    // 设置边框（使用实际行数）
    applyTableBorders(sheet, 0, 0, totalRows, totalCols);

    // 设置数字格式
    applyNumberFormats(sheet, allDates, config);

    // 设置交替行背景色
    applyAlternateRowColors(sheet, totalRows, totalCols);

  } catch (error) {
    console.log("[applyPersonalCalendarStyles] 应用样式时出错:", error.message);
  }
}

/**
 * 应用行业日历样式
 * @param {Sheet} sheet - 工作表对象
 * @param {Array} sortedDates - 排序后的日期
 * @param {Object} config - 配置对象
 * @param {number} totalRows - 实际数据行数
 */
function applyIndustryCalendarStyles(sheet, sortedDates, config, totalRows) {
  try {
    const totalCols = sortedDates.length * config.columnsPerDate + 1;

    // 设置列宽
    sheet.setColumnWidth(0, CONFIG.styles.dimensions.industryColumnWidth);
    for (let i = 0; i < sortedDates.length; i++) {
      config.columns.forEach((col, colIndex) => {
        const actualCol = i * config.columnsPerDate + 1 + colIndex;
        if (col.width) {
          sheet.setColumnWidth(actualCol, col.width);
        }
      });
    }

    // 设置标题行样式
    applyHeaderStyles(sheet, 0, 0, 1, totalCols);
    applyHeaderStyles(sheet, 1, 0, 1, totalCols);

    // 设置左侧列样式（只对数据行，不覆盖标题行）
    applyLeftColumnStyles(sheet, totalRows);

    // 设置边框（使用实际行数）
    applyTableBorders(sheet, 0, 0, totalRows, totalCols);

    // 设置数字格式
    applyIndustryNumberFormats(sheet, sortedDates, config);

    // 设置交替行背景色
    applyAlternateRowColors(sheet, totalRows, totalCols);

  } catch (error) {
    console.log("[applyIndustryCalendarStyles] 应用样式时出错:", error.message);
  }
}

/**
 * 应用表头样式
 * @param {Sheet} sheet - 工作表对象
 * @param {number} startRow - 开始行
 * @param {number} startCol - 开始列
 * @param {number} numRows - 行数
 * @param {number} numCols - 列数
 */
function applyHeaderStyles(sheet, startRow, startCol, numRows, numCols) {
  try {
    const headerRange = sheet.getRange(startRow, startCol, numRows, numCols);
    headerRange.setFontWeight('bold');
    headerRange.setBackgroundColor(CONFIG.styles.colors.headerBackground);
    headerRange.setFontColor(CONFIG.styles.colors.headerFont);
  } catch (error) {
    console.log("[applyHeaderStyles] 设置表头样式失败:", error.message);
  }
}

/**
 * 应用左侧列样式（只对数据行，不覆盖标题行）
 * @param {Sheet} sheet - 工作表对象
 * @param {number} totalRows - 总行数
 */
function applyLeftColumnStyles(sheet, totalRows) {
  try {
    // 只对数据行设置左侧列背景色（从第3行开始，跳过标题行）
    if (totalRows > 2) {
      const dataRowsRange = sheet.getRange(2, 0, totalRows - 2, 1);
      dataRowsRange.setBackgroundColor(CONFIG.styles.colors.leftColumnBackground);
      dataRowsRange.setFontWeight('bold');
    }
  } catch (error) {
    console.log("[applyLeftColumnStyles] 设置左侧列样式失败:", error.message);
  }
}

/**
 * 应用表格边框
 * @param {Sheet} sheet - 工作表对象
 * @param {number} startRow - 开始行
 * @param {number} startCol - 开始列
 * @param {number} numRows - 行数
 * @param {number} numCols - 列数
 */
function applyTableBorders(sheet, startRow, startCol, numRows, numCols) {
  try {
    const tableRange = sheet.getRange(startRow, startCol, numRows, numCols);
    // 使用原始文件中的正确边框设置方法
    if (tableRange.setBorder) {
      tableRange.setBorder(
        { top: true, bottom: true, left: true, right: true, vertical: true, horizontal: true },
        CONFIG.styles.colors.borderColor,
        "solid"
      );
    }
  } catch (error) {
    console.log("[applyTableBorders] 设置边框失败:", error.message);
  }
}

/**
 * 应用个人日历数字格式
 * @param {Sheet} sheet - 工作表对象
 * @param {Array} allDates - 所有日期
 * @param {Object} config - 配置对象
 */
function applyNumberFormats(sheet, allDates, config) {
  try {
    allDates.forEach((_, dateIndex) => {
      const colOffset = dateIndex * config.columnsPerDate + 1;
      config.columns.forEach((col, colIndex) => {
        if (col.format) {
          const colRange = sheet.getRange(2, colOffset + colIndex, 1000, 1);
          colRange.setNumberFormat(col.format);
        }
      });
    });
  } catch (error) {
    console.log("[applyNumberFormats] 设置数字格式失败:", error.message);
  }
}

/**
 * 应用行业日历数字格式
 * @param {Sheet} sheet - 工作表对象
 * @param {Array} sortedDates - 排序后的日期
 * @param {Object} config - 配置对象
 */
function applyIndustryNumberFormats(sheet, sortedDates, config) {
  try {
    sortedDates.forEach((_, dateIndex) => {
      const colOffset = dateIndex * config.columnsPerDate + 1;
      config.columns.forEach((col, colIndex) => {
        if (col.format) {
          const colRange = sheet.getRange(2, colOffset + colIndex, 1000, 1);
          colRange.setNumberFormat(col.format);
        }
      });
    });
  } catch (error) {
    console.log("[applyIndustryNumberFormats] 设置数字格式失败:", error.message);
  }
}

/**
 * 应用交替行背景色
 * @param {Sheet} sheet - 工作表对象
 * @param {number} totalRows - 总行数
 * @param {number} totalCols - 总列数
 */
function applyAlternateRowColors(sheet, totalRows, totalCols) {
  try {
    // 为数据区域设置交替行色（从第3行开始）
    for (let row = 2; row < totalRows; row++) {
      if (row % 2 === 0) { // 偶数行
        try {
          const rowRange = sheet.getRange(row, 0, 1, totalCols);
          rowRange.setBackgroundColor(CONFIG.styles.colors.alternateRowBackground);
        } catch (_bgError) {
          // 背景色设置失败，忽略
        }
      }
    }
  } catch (error) {
    console.log("[applyAlternateRowColors] 设置交替行背景色失败:", error.message);
  }
}

/**
 * 应用冻结窗格
 * @param {Sheet} sheet - 工作表对象
 * @param {number} rows - 冻结行数
 * @param {number} columns - 冻结列数
 */
function applyFreezePanes(sheet, rows, columns) {
  try {
    sheet.setFrozenRowCount(rows);
    sheet.setFrozenColumnCount(columns);
  } catch (error) {
    console.log("[applyFreezePanes] 设置冻结窗格失败:", error.message);
  }
}

// 启动脚本
await main();
